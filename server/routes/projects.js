const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const { addGroupContext, filterByGroup, addGroupToData, ensureGroupAccess } = require('../middleware/groupAuth');
const { resolveProjectId } = require('../middleware/elementIdResolver');
const { generateElementId } = require('../utils/elementIdGenerator');
const Project = require('../models/Project');
const User = require('../models/User');

// Create a new project
router.post('/', auth, addGroupContext, async (req, res) => {
  try {
    console.log('Creating new project with data:', req.body);
    console.log('User from token:', req.user);

    if (!req.user || !req.user.userId) {
      console.error('No user ID found in request');
      return res.status(400).json({ message: 'User ID not found in request' });
    }

    const projectData = addGroupToData(req, {
      name: req.body.name,
      description: req.body.description,
      createdBy: req.user.userId
    });

    // Add the creator as a member if addSelf is true (default behavior)
    if (req.body.addSelf !== false) {
      projectData.members = [{
        user: req.user.userId,
        roles: []
      }];
    } else {
      projectData.members = [];
    }

    const project = new Project(projectData);
    const newProject = await project.save();

    // Generate ElementID after project is saved (so we have the ObjectId)
    const elementId = await generateElementId(newProject.group, newProject._id, 'project');
    newProject.elementId = elementId;
    await newProject.save();

    console.log('Project saved with ElementID:', newProject);

    const populatedProject = await Project.findById(newProject._id)
      .populate('createdBy', 'username firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar');
    console.log('Populated project:', populatedProject);

    // Convert ObjectIds to strings
    const responseData = {
      ...populatedProject.toObject(),
      _id: populatedProject._id.toString(),
      createdBy: {
        _id: populatedProject.createdBy._id.toString(),
        username: populatedProject.createdBy.username
      }
    };
    console.log('Response data:', responseData);

    res.status(201).json(responseData);
  } catch (error) {
    console.error('Error creating project:', error);
    res.status(400).json({ message: error.message });
  }
});

// Get all projects
router.get('/', auth, addGroupContext, filterByGroup, async (req, res) => {
  try {
    const filter = req.groupFilter || {};
    const projects = await Project.find(filter)
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar');
    res.json(projects);
  } catch (err) {
    console.error(err);
    res.status(500).send('Server Error');
  }
});

// Get a specific project
router.get('/:id', auth, addGroupContext, resolveProjectId, async (req, res) => {
  try {
    const project = await Project.findById(req.resolvedId)
      .populate('createdBy', 'username firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar')
      .populate('comments.user', 'username color firstName lastName avatar');

    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }

    res.json(project);
  } catch (error) {
    console.error('Error fetching project:', error);
    res.status(500).json({ message: error.message });
  }
});

// Update a project
router.put('/:id', auth, addGroupContext, ensureGroupAccess(Project), async (req, res) => {
  try {
    // Resolve ElementID to ObjectID if needed
    let resolvedProjectId = req.params.id;
    if (req.params.id && typeof req.params.id === 'string') {
      const mongoose = require('mongoose');
      if (!mongoose.Types.ObjectId.isValid(req.params.id) || req.params.id.length !== 24) {
        // It's an ElementID, resolve it to ObjectID
        const projectDoc = await Project.findOne({
          elementId: req.params.id,
          group: req.userGroup._id
        });
        if (!projectDoc) {
          return res.status(404).json({ message: 'Project not found' });
        }
        resolvedProjectId = projectDoc._id;
      }
    }

    const project = await Project.findById(resolvedProjectId);

    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }

    project.name = req.body.name || project.name;
    project.description = req.body.description || project.description;

    const updatedProject = await project.save();
    const populatedProject = await Project.findById(updatedProject._id)
      .populate('createdBy', 'username firstName lastName avatar');

    res.json(populatedProject);
  } catch (error) {
    console.error('Error updating project:', error);
    res.status(400).json({ message: error.message });
  }
});

// Delete a project
router.delete('/:id', auth, addGroupContext, ensureGroupAccess(Project), async (req, res) => {
  try {
    // Resolve ElementID to ObjectID if needed
    let resolvedProjectId = req.params.id;
    if (req.params.id && typeof req.params.id === 'string') {
      const mongoose = require('mongoose');
      if (!mongoose.Types.ObjectId.isValid(req.params.id) || req.params.id.length !== 24) {
        // It's an ElementID, resolve it to ObjectID
        const projectDoc = await Project.findOne({
          elementId: req.params.id,
          group: req.userGroup._id
        });
        if (!projectDoc) {
          return res.status(404).json({ message: 'Project not found' });
        }
        resolvedProjectId = projectDoc._id;
      }
    }

    const project = await Project.findById(resolvedProjectId);

    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }

    await project.deleteOne();
    res.json({ message: 'Project deleted' });
  } catch (error) {
    console.error('Error deleting project:', error);
    res.status(500).json({ message: error.message });
  }
});

// Add comment to project
router.post('/:id/comments', auth, addGroupContext, ensureGroupAccess(Project), async (req, res) => {
  try {
    // Resolve ElementID to ObjectID if needed
    let resolvedProjectId = req.params.id;
    if (req.params.id && typeof req.params.id === 'string') {
      const mongoose = require('mongoose');
      if (!mongoose.Types.ObjectId.isValid(req.params.id) || req.params.id.length !== 24) {
        // It's an ElementID, resolve it to ObjectID
        const projectDoc = await Project.findOne({
          elementId: req.params.id,
          group: req.userGroup._id
        });
        if (!projectDoc) {
          return res.status(404).json({ message: 'Project not found' });
        }
        resolvedProjectId = projectDoc._id;
      }
    }

    const project = await Project.findById(resolvedProjectId);
    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }

    const newComment = {
      text: req.body.text,
      user: req.user.userId,
      createdAt: new Date()
    };

    project.comments.push(newComment);
    await project.save();

    // Populate the user data for the response
    const populatedProject = await Project.findById(project._id)
      .populate('comments.user', 'username color firstName lastName avatar');

    const addedComment = populatedProject.comments[populatedProject.comments.length - 1];
    res.status(201).json(addedComment);
  } catch (error) {
    console.error('Error adding comment:', error);
    res.status(500).json({ message: error.message });
  }
});

// Add linked text to project
router.post('/:id/linked-texts', auth, addGroupContext, ensureGroupAccess(Project), async (req, res) => {
  try {
    // Resolve ElementID to ObjectID if needed
    let resolvedProjectId = req.params.id;
    if (req.params.id && typeof req.params.id === 'string') {
      const mongoose = require('mongoose');
      if (!mongoose.Types.ObjectId.isValid(req.params.id) || req.params.id.length !== 24) {
        // It's an ElementID, resolve it to ObjectID
        const projectDoc = await Project.findOne({
          elementId: req.params.id,
          group: req.userGroup._id
        });
        if (!projectDoc) {
          return res.status(404).json({ message: 'Project not found' });
        }
        resolvedProjectId = projectDoc._id;
      }
    }

    const project = await Project.findById(resolvedProjectId);
    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }

    const newLinkedText = {
      id: req.body.id,
      text: req.body.text,
      startOffset: req.body.startOffset,
      endOffset: req.body.endOffset,
      commentId: req.body.commentId,
      highlightColor: req.body.highlightColor,
      createdAt: new Date()
    };

    project.linkedTexts.push(newLinkedText);
    await project.save();

    res.status(201).json(newLinkedText);
  } catch (error) {
    console.error('Error adding linked text:', error);
    res.status(500).json({ message: error.message });
  }
});

// Get all linked texts for a project
router.get('/:id/linked-texts', auth, addGroupContext, ensureGroupAccess(Project), async (req, res) => {
  try {
    // Resolve ElementID to ObjectID if needed
    let resolvedProjectId = req.params.id;
    if (req.params.id && typeof req.params.id === 'string') {
      const mongoose = require('mongoose');
      if (!mongoose.Types.ObjectId.isValid(req.params.id) || req.params.id.length !== 24) {
        // It's an ElementID, resolve it to ObjectID
        const projectDoc = await Project.findOne({
          elementId: req.params.id,
          group: req.userGroup._id
        });
        if (!projectDoc) {
          return res.status(404).json({ message: 'Project not found' });
        }
        resolvedProjectId = projectDoc._id;
      }
    }

    const project = await Project.findById(resolvedProjectId);

    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }

    res.json(project.linkedTexts || []);
  } catch (error) {
    console.error('Error fetching linked texts:', error);
    res.status(500).json({ message: error.message });
  }
});

// Add member to project
router.post('/:projectId/members', auth, addGroupContext, ensureGroupAccess(Project), async (req, res) => {
  try {
    const { userId, roles } = req.body;
    // Resolve ElementID to ObjectID if needed
    let resolvedProjectId = req.params.projectId;
    if (req.params.projectId && typeof req.params.projectId === 'string') {
      const mongoose = require('mongoose');
      if (!mongoose.Types.ObjectId.isValid(req.params.projectId) || req.params.projectId.length !== 24) {
        // It's an ElementID, resolve it to ObjectID
        const projectDoc = await Project.findOne({
          elementId: req.params.projectId,
          group: req.userGroup._id
        });
        if (!projectDoc) {
          return res.status(404).json({ message: 'Project not found' });
        }
        resolvedProjectId = projectDoc._id;
      }
    }

    const project = await Project.findById(resolvedProjectId);

    if (!project) {
      return res.status(404).json({ msg: 'Project not found' });
    }

    // Check if user exists and belongs to the same group
    const userFilter = req.isSuperUser ? { _id: userId } : { _id: userId, group: req.userGroup._id, groupStatus: 'active' };
    const user = await User.findOne(userFilter);
    if (!user) {
      return res.status(404).json({ msg: 'User not found or not in your group' });
    }

    // Check if user is already a member
    const existingMemberIndex = project.members.findIndex(
      member => member.user.toString() === userId
    );

    if (existingMemberIndex >= 0) {
      // Add new roles to existing member
      const existingRoles = project.members[existingMemberIndex].roles;
      roles.forEach(role => {
        if (!existingRoles.includes(role)) {
          existingRoles.push(role);
        }
      });
    } else {
      // Add new member
      project.members.push({ user: userId, roles });
    }

    await project.save();

    // Populate user data before sending response
    const populatedProject = await Project.findById(project._id)
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar');

    res.json(populatedProject);
  } catch (err) {
    console.error(err);
    res.status(500).send('Server Error');
  }
});

// Remove member from project
router.delete('/:projectId/members/:userId', auth, addGroupContext, ensureGroupAccess(Project), async (req, res) => {
  try {
    // Resolve ElementID to ObjectID if needed
    let resolvedProjectId = req.params.projectId;
    if (req.params.projectId && typeof req.params.projectId === 'string') {
      const mongoose = require('mongoose');
      if (!mongoose.Types.ObjectId.isValid(req.params.projectId) || req.params.projectId.length !== 24) {
        // It's an ElementID, resolve it to ObjectID
        const projectDoc = await Project.findOne({
          elementId: req.params.projectId,
          group: req.userGroup._id
        });
        if (!projectDoc) {
          return res.status(404).json({ message: 'Project not found' });
        }
        resolvedProjectId = projectDoc._id;
      }
    }

    const project = await Project.findById(resolvedProjectId);

    if (!project) {
      return res.status(404).json({ msg: 'Project not found' });
    }

    project.members = project.members.filter(
      member => member.user.toString() !== req.params.userId
    );

    await project.save();

    // Populate user data before sending response
    const populatedProject = await Project.findById(project._id)
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar');

    res.json(populatedProject);
  } catch (err) {
    console.error(err);
    res.status(500).send('Server Error');
  }
});

// Update member roles
router.put('/:projectId/members/:userId', auth, addGroupContext, ensureGroupAccess(Project), async (req, res) => {
  try {
    const { roles } = req.body;
    // Resolve ElementID to ObjectID if needed
    let resolvedProjectId = req.params.projectId;
    if (req.params.projectId && typeof req.params.projectId === 'string') {
      const mongoose = require('mongoose');
      if (!mongoose.Types.ObjectId.isValid(req.params.projectId) || req.params.projectId.length !== 24) {
        // It's an ElementID, resolve it to ObjectID
        const projectDoc = await Project.findOne({
          elementId: req.params.projectId,
          group: req.userGroup._id
        });
        if (!projectDoc) {
          return res.status(404).json({ message: 'Project not found' });
        }
        resolvedProjectId = projectDoc._id;
      }
    }

    const project = await Project.findById(resolvedProjectId);

    if (!project) {
      return res.status(404).json({ msg: 'Project not found' });
    }

    const memberIndex = project.members.findIndex(
      member => member.user.toString() === req.params.userId
    );

    if (memberIndex === -1) {
      return res.status(404).json({ msg: 'Member not found' });
    }

    project.members[memberIndex].roles = roles;
    await project.save();

    // Return the project with populated user data
    const populatedProject = await Project.findById(project._id)
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar');

    res.json(populatedProject);
  } catch (err) {
    console.error(err);
    res.status(500).send('Server Error');
  }
});

module.exports = router;