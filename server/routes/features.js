const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const { addGroupContext, filterByGroup } = require('../middleware/groupAuth');
const { resolveFeatureId, resolveElementId } = require('../middleware/elementIdResolver');
const { generateElementId } = require('../utils/elementIdGenerator');
const Feature = require('../models/Feature');
const Project = require('../models/Project');
const User = require('../models/User');

// Debug logging middleware for features routes
router.use((req, res, next) => {
  console.log('Features route hit:', req.method, req.path);
  next();
});

// Get all features
router.get('/', auth, addGroupContext, filterByGroup, async (req, res) => {
  try {
    const filter = req.isSuperUser ? {} : req.groupFilter;
    const features = await Feature.find(filter)
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar')
      .populate('project', 'name')
      .sort({ createdAt: -1 });

    res.json(features);
  } catch (err) {
    console.error(err);
    res.status(500).send('Server Error');
  }
});

// Create a new feature
router.post('/', auth, addGroupContext, async (req, res) => {
  console.log('Creating new feature with data:', req.body);
  try {
    const { project, title, description } = req.body;

    // Resolve project ElementID to ObjectID if needed
    let resolvedProjectId = project;
    if (project && typeof project === 'string') {
      // Check if it's an ElementID (not a valid ObjectID)
      const mongoose = require('mongoose');
      if (!mongoose.Types.ObjectId.isValid(project) || project.length !== 24) {
        // It's an ElementID, resolve it to ObjectID
        const projectDoc = await Project.findOne({
          elementId: project,
          group: req.userGroup._id
        });
        if (!projectDoc) {
          return res.status(404).json({ message: 'Project not found' });
        }
        resolvedProjectId = projectDoc._id;
        console.log('Resolved ElementID', project, 'to ObjectID', resolvedProjectId);
      }
    }

    // Get user's group context
    const user = await User.findById(req.user.userId).populate('group');
    if (!user) {
      return res.status(401).json({ message: 'User not found' });
    }

    // Verify user has access to create features in this group
    if (!user.isSuperUser && user.groupStatus !== 'active') {
      return res.status(403).json({ message: 'Account is inactive' });
    }

    const feature = new Feature({
      project: resolvedProjectId,
      group: user.group._id, // Set the group for multi-tenancy
      versions: [{
        version: 1,
        title,
        description,
        createdBy: req.user.userId
      }],
      createdBy: req.user.userId,
      members: []
    });

    console.log('Saving new feature:', feature);
    await feature.save();

    // Generate ElementID after feature is saved
    const elementId = await generateElementId(feature.group, feature.project, 'feature');
    feature.elementId = elementId;
    await feature.save();

    // Comment out or remove this section
// Add creator to project members if not already a member
//const projectDoc = await Project.findById(project);
//if (projectDoc) {
  //const isMember = projectDoc.members.some(
    //member => member.user.toString() === req.user.userId
  //);

  //if (!isMember) {
   // projectDoc.members.push({
    //  user: req.user.userId,
     // roles: ['project_manager']
    //});
    //await projectDoc.save();
 // }
//}

    // Populate the feature with user data before sending response
    const populatedFeature = await Feature.findById(feature._id)
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar');

    console.log('Feature created successfully:', populatedFeature);
    res.json(populatedFeature);
  } catch (err) {
    console.error('Error creating feature:', err);
    res.status(500).json({ message: err.message });
  }
});

// Get all features for a project
router.get('/project/:projectId', auth, addGroupContext, resolveElementId('Project', 'projectId'), async (req, res) => {
  console.log('Fetching features for project:', req.params.projectId);
  console.log('Resolved project ID:', req.resolvedProjectId);
  console.log('User:', req.user);
  try {
    const features = await Feature.find({ project: req.resolvedProjectId })
      .populate('createdBy', 'username firstName lastName avatar')
      .populate('versions.createdBy', 'username firstName lastName avatar')
      .populate('versions.comments.user', 'username firstName lastName avatar')
      .sort({ createdAt: -1 });

    console.log('Found features:', features.length);
    console.log('Features data:', JSON.stringify(features, null, 2));

    res.json(features);
  } catch (error) {
    console.error('Error fetching features:', error);
    console.error('Error details:', {
      message: error.message,
      stack: error.stack
    });
    res.status(500).json({ message: error.message });
  }
});

// Get a specific feature
router.get('/:id', auth, addGroupContext, resolveFeatureId, async (req, res) => {
  try {
    let query = Feature.findById(req.resolvedId)
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar')
      .populate('versions.comments.user', 'username color firstName lastName avatar')
      .populate('versions.releaseTags.addedBy', 'username firstName lastName');

    // Handle populate query parameter
    if (req.query.populate) {
      const populateFields = req.query.populate.split(',');
      populateFields.forEach(field => {
        if (field === 'project') {
          query = query.populate('project', 'name elementId createdBy');
        }
      });
    }

    const feature = await query;

    if (!feature) {
      return res.status(404).json({ msg: 'Feature not found' });
    }

    res.json(feature);
  } catch (err) {
    console.error(err);
    res.status(500).send('Server Error');
  }
});

// Update a feature
router.put('/:id', auth, addGroupContext, resolveFeatureId, async (req, res) => {
  try {
    const feature = await Feature.findById(req.resolvedFeatureId);

    if (!feature) {
      return res.status(404).json({ message: 'Feature not found' });
    }

    // Get the current version's comments and data
    const currentVersion = feature.versions[feature.versions.length - 1];
    const currentComments = currentVersion.comments || [];

    // Helper function to strip HTML tags for change tracking
    const stripHtml = (html) => {
      if (!html) return html;
      // Remove HTML tags and decode HTML entities
      return html
        .replace(/<[^>]*>/g, '') // Remove HTML tags
        .replace(/&nbsp;/g, ' ') // Replace non-breaking spaces
        .replace(/&amp;/g, '&')  // Replace &amp; with &
        .replace(/&lt;/g, '<')   // Replace &lt; with <
        .replace(/&gt;/g, '>')   // Replace &gt; with >
        .replace(/&quot;/g, '"') // Replace &quot; with "
        .replace(/&#39;/g, "'")  // Replace &#39; with '
        .replace(/\s+/g, ' ')    // Replace multiple whitespace with single space
        .trim();
    };

    // Track changes between versions
    const changes = [];

    // Check title changes
    if (req.body.title !== currentVersion.title) {
      changes.push({
        field: 'title',
        from: currentVersion.title,
        to: req.body.title
      });
    }

    // Check description changes
    if (req.body.description !== currentVersion.description) {
      changes.push({
        field: 'description',
        from: stripHtml(currentVersion.description),
        to: stripHtml(req.body.description)
      });
    }

    // Check state changes
    if (req.body.state && req.body.state !== currentVersion.state) {
      changes.push({
        field: 'state',
        from: currentVersion.state,
        to: req.body.state
      });
    }

    const newVersion = {
      version: feature.currentVersion + 1,
      title: req.body.title || currentVersion.title,
      description: req.body.description || currentVersion.description,
      state: req.body.state || 'New', // Reset to 'New' state for new versions unless explicitly specified
      labels: currentVersion.labels || [],
      createdBy: req.user.userId,
      comments: [...currentComments], // Copy the comments from the current version
      changes: changes // Add the tracked changes
    };

    feature.versions.push(newVersion);
    feature.currentVersion = feature.versions.length;

    const updatedFeature = await feature.save();
    const populatedFeature = await Feature.findById(updatedFeature._id)
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar')
      .populate('versions.createdBy', 'username firstName lastName avatar')
      .populate('versions.comments.user', 'username color firstName lastName avatar');

    res.json(populatedFeature);
  } catch (error) {
    console.error('Error updating feature:', error);
    res.status(400).json({ message: error.message });
  }
});

// Add member to feature
router.post('/:featureId/members', auth, async (req, res) => {
  try {
    const { userId, roles } = req.body;
    const feature = await Feature.findById(req.params.featureId);

    if (!feature) {
      return res.status(404).json({ msg: 'Feature not found' });
    }

    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }

    // Check if user is already a member
    const existingMemberIndex = feature.members.findIndex(
      member => member.user.toString() === userId
    );

    if (existingMemberIndex >= 0) {
      // Add new roles to existing member
      const existingRoles = feature.members[existingMemberIndex].roles;
      roles.forEach(role => {
        if (!existingRoles.includes(role)) {
          existingRoles.push(role);
        }
      });
    } else {
      // Add new member
      feature.members.push({ user: userId, roles });
    }

    await feature.save();

    // Add user to project members if not already a member (cascading up)
    const project = await Project.findById(feature.project);
    if (project) {
      const isMember = project.members.some(
        member => member.user.toString() === userId
      );

      if (!isMember) {
        project.members.push({
          user: userId,
          roles: roles
        });
        await project.save();
      }
    }

    // Return the feature with populated user data
    const populatedFeature = await Feature.findById(feature._id)
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar');

    res.json(populatedFeature);
  } catch (err) {
    console.error(err);
    res.status(500).send('Server Error');
  }
});

// Remove member from feature
router.delete('/:featureId/members/:userId', auth, async (req, res) => {
  try {
    const feature = await Feature.findById(req.params.featureId);

    if (!feature) {
      return res.status(404).json({ msg: 'Feature not found' });
    }

    // Check if user is assigned to any child requirements
    const Requirement = require('../models/Requirement');
    const childRequirements = await Requirement.find({ feature: req.params.featureId });

    const assignedRequirements = [];
    for (const requirement of childRequirements) {
      const isAssigned = requirement.members.some(
        member => member.user.toString() === req.params.userId
      );

      if (isAssigned) {
        const currentVersion = requirement.versions[requirement.versions.length - 1];
        assignedRequirements.push(currentVersion.title);
      }
    }

    if (assignedRequirements.length > 0) {
      return res.status(400).json({
        msg: 'Cannot remove user from feature',
        reason: 'User is assigned to child requirements',
        assignedRequirements: assignedRequirements
      });
    }

    feature.members = feature.members.filter(
      member => member.user.toString() !== req.params.userId
    );

    await feature.save();

    // Return the feature with populated user data
    const populatedFeature = await Feature.findById(feature._id)
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar');

    res.json(populatedFeature);
  } catch (err) {
    console.error(err);
    res.status(500).send('Server Error');
  }
});

// Update member roles
router.put('/:featureId/members/:userId', auth, async (req, res) => {
  try {
    const { roles } = req.body;
    const feature = await Feature.findById(req.params.featureId);

    if (!feature) {
      return res.status(404).json({ msg: 'Feature not found' });
    }

    const memberIndex = feature.members.findIndex(
      member => member.user.toString() === req.params.userId
    );

    if (memberIndex === -1) {
      return res.status(404).json({ msg: 'Member not found' });
    }

    feature.members[memberIndex].roles = roles;
    await feature.save();

    // Return the feature with populated user data
    const populatedFeature = await Feature.findById(feature._id)
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar');

    res.json(populatedFeature);
  } catch (err) {
    console.error(err);
    res.status(500).send('Server Error');
  }
});

// Add comment to feature
router.post('/:featureId/comments', auth, addGroupContext, async (req, res) => {
  try {
    const { text, version } = req.body;

    // Resolve ElementID to ObjectID if needed
    let resolvedFeatureId = req.params.featureId;
    if (req.params.featureId && typeof req.params.featureId === 'string') {
      const mongoose = require('mongoose');
      if (!mongoose.Types.ObjectId.isValid(req.params.featureId) || req.params.featureId.length !== 24) {
        // It's an ElementID, resolve it to ObjectID
        const featureDoc = await Feature.findOne({
          elementId: req.params.featureId,
          group: req.userGroup._id
        });
        if (!featureDoc) {
          return res.status(404).json({ message: 'Feature not found' });
        }
        resolvedFeatureId = featureDoc._id;
      }
    }

    const feature = await Feature.findById(resolvedFeatureId);

    if (!feature) {
      return res.status(404).json({ msg: 'Feature not found' });
    }

    // Find the version to add the comment to
    const versionIndex = feature.versions.findIndex(v => v.version === version);
    if (versionIndex === -1) {
      return res.status(404).json({ msg: 'Version not found' });
    }

    // Create the comment
    const comment = {
      text,
      user: req.user.userId,
      version,
      createdAt: new Date()
    };

    // Add comment to the version
    if (!feature.versions[versionIndex].comments) {
      feature.versions[versionIndex].comments = [];
    }
    feature.versions[versionIndex].comments.push(comment);

    await feature.save();

    // Return the feature with populated user data
    const populatedFeature = await Feature.findById(feature._id)
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar')
      .populate('versions.comments.user', 'username color firstName lastName avatar');

    res.json(populatedFeature);
  } catch (err) {
    console.error(err);
    res.status(500).send('Server Error');
  }
});

// Add a linked text to a feature
router.post('/:featureId/linked-texts', auth, addGroupContext, async (req, res) => {
  try {
    // Resolve ElementID to ObjectID if needed
    let resolvedFeatureId = req.params.featureId;
    if (req.params.featureId && typeof req.params.featureId === 'string') {
      const mongoose = require('mongoose');
      if (!mongoose.Types.ObjectId.isValid(req.params.featureId) || req.params.featureId.length !== 24) {
        // It's an ElementID, resolve it to ObjectID
        const featureDoc = await Feature.findOne({
          elementId: req.params.featureId,
          group: req.userGroup._id
        });
        if (!featureDoc) {
          return res.status(404).json({ message: 'Feature not found' });
        }
        resolvedFeatureId = featureDoc._id;
      }
    }

    const feature = await Feature.findById(resolvedFeatureId);

    if (!feature) {
      return res.status(404).json({ message: 'Feature not found' });
    }

    const { id, text, commentId, startOffset, endOffset, highlightColor, version } = req.body;

    // Create the linked text
    const newLinkedText = {
      id,
      text,
      commentId,
      startOffset,
      endOffset,
      highlightColor: highlightColor || '#1976d2',
      version: version || feature.currentVersion,
      createdAt: new Date()
    };

    // Add the linked text to the feature
    feature.linkedTexts.push(newLinkedText);

    const updatedFeature = await feature.save();
    const populatedFeature = await Feature.findById(updatedFeature._id)
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar')
      .populate('versions.comments.user', 'username color firstName lastName avatar');

    res.json(newLinkedText);
  } catch (error) {
    console.error('Error adding linked text:', error);
    res.status(500).json({ message: error.message });
  }
});

// Get all linked texts for a feature
router.get('/:featureId/linked-texts', auth, addGroupContext, async (req, res) => {
  try {
    // Resolve ElementID to ObjectID if needed
    let resolvedFeatureId = req.params.featureId;
    if (req.params.featureId && typeof req.params.featureId === 'string') {
      const mongoose = require('mongoose');
      if (!mongoose.Types.ObjectId.isValid(req.params.featureId) || req.params.featureId.length !== 24) {
        // It's an ElementID, resolve it to ObjectID
        const featureDoc = await Feature.findOne({
          elementId: req.params.featureId,
          group: req.userGroup._id
        });
        if (!featureDoc) {
          return res.status(404).json({ message: 'Feature not found' });
        }
        resolvedFeatureId = featureDoc._id;
      }
    }

    const feature = await Feature.findById(resolvedFeatureId);

    if (!feature) {
      return res.status(404).json({ message: 'Feature not found' });
    }

    // Filter by version if specified
    const version = req.query.version;
    let linkedTexts = feature.linkedTexts || [];

    if (version) {
      linkedTexts = linkedTexts.filter(lt => lt.version === parseInt(version));
    }

    res.json(linkedTexts);
  } catch (error) {
    console.error('Error fetching linked texts:', error);
    res.status(500).json({ message: error.message });
  }
});

// Delete a linked text from a feature
router.delete('/:featureId/linked-texts/:linkId', auth, addGroupContext, async (req, res) => {
  try {
    // Resolve ElementID to ObjectID if needed
    let resolvedFeatureId = req.params.featureId;
    if (req.params.featureId && typeof req.params.featureId === 'string') {
      const mongoose = require('mongoose');
      if (!mongoose.Types.ObjectId.isValid(req.params.featureId) || req.params.featureId.length !== 24) {
        // It's an ElementID, resolve it to ObjectID
        const featureDoc = await Feature.findOne({
          elementId: req.params.featureId,
          group: req.userGroup._id
        });
        if (!featureDoc) {
          return res.status(404).json({ message: 'Feature not found' });
        }
        resolvedFeatureId = featureDoc._id;
      }
    }

    const feature = await Feature.findById(resolvedFeatureId);

    if (!feature) {
      return res.status(404).json({ message: 'Feature not found' });
    }

    // Remove the linked text
    feature.linkedTexts = feature.linkedTexts.filter(
      lt => lt.id !== req.params.linkId
    );

    await feature.save();
    res.json({ message: 'Linked text removed successfully' });
  } catch (error) {
    console.error('Error removing linked text:', error);
    res.status(500).json({ message: error.message });
  }
});

// Transition feature state
router.post('/:featureId/transition-state', auth, async (req, res) => {
  try {
    const { newState } = req.body;
    const feature = await Feature.findById(req.params.featureId);

    if (!feature) {
      return res.status(404).json({ msg: 'Feature not found' });
    }

    // Special validation for closing feature
    if (newState === 'CLOSED') {
      const validation = await feature.canBeClosed();
      if (!validation.canClose) {
        return res.status(400).json({
          msg: 'Cannot close feature',
          reason: validation.reason
        });
      }
    }

    await feature.transitionState(newState);

    // Return the feature with populated user data
    const populatedFeature = await Feature.findById(feature._id)
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar')
      .populate('versions.comments.user', 'username color firstName lastName avatar');

    res.json(populatedFeature);
  } catch (err) {
    console.error('Error transitioning feature state:', err);
    res.status(400).json({ msg: err.message });
  }
});

// Check if feature can be closed
router.get('/:featureId/can-close', auth, addGroupContext, async (req, res) => {
  try {
    // Resolve ElementID to ObjectID if needed
    let resolvedFeatureId = req.params.featureId;
    if (req.params.featureId && typeof req.params.featureId === 'string') {
      const mongoose = require('mongoose');
      if (!mongoose.Types.ObjectId.isValid(req.params.featureId) || req.params.featureId.length !== 24) {
        // It's an ElementID, resolve it to ObjectID
        const featureDoc = await Feature.findOne({
          elementId: req.params.featureId,
          group: req.userGroup._id
        });
        if (!featureDoc) {
          return res.status(404).json({ message: 'Feature not found' });
        }
        resolvedFeatureId = featureDoc._id;
      }
    }

    const feature = await Feature.findById(resolvedFeatureId);

    if (!feature) {
      return res.status(404).json({ msg: 'Feature not found' });
    }

    const validation = await feature.canBeClosed();
    res.json(validation);
  } catch (err) {
    console.error('Error checking if feature can be closed:', err);
    res.status(500).send('Server Error');
  }
});

// Validate and sync feature members with child requirements
router.post('/:featureId/validate-members', auth, addGroupContext, async (req, res) => {
  try {
    // Resolve ElementID to ObjectID if needed
    let resolvedFeatureId = req.params.featureId;
    if (req.params.featureId && typeof req.params.featureId === 'string') {
      const mongoose = require('mongoose');
      if (!mongoose.Types.ObjectId.isValid(req.params.featureId) || req.params.featureId.length !== 24) {
        // It's an ElementID, resolve it to ObjectID
        const featureDoc = await Feature.findOne({
          elementId: req.params.featureId,
          group: req.userGroup._id
        });
        if (!featureDoc) {
          return res.status(404).json({ message: 'Feature not found' });
        }
        resolvedFeatureId = featureDoc._id;
      }
    }

    const feature = await Feature.findById(resolvedFeatureId);

    if (!feature) {
      return res.status(404).json({ msg: 'Feature not found' });
    }

    // Get all child requirements
    const Requirement = require('../models/Requirement');
    const childRequirements = await Requirement.find({ feature: resolvedFeatureId });

    // Collect all unique users from child requirements
    const childRequirementUsers = new Map(); // userId -> { roles: Set, requirements: [] }

    for (const requirement of childRequirements) {
      const currentVersion = requirement.versions[requirement.versions.length - 1];

      for (const member of requirement.members) {
        const userId = member.user.toString();

        if (!childRequirementUsers.has(userId)) {
          childRequirementUsers.set(userId, {
            roles: new Set(),
            requirements: []
          });
        }

        const userData = childRequirementUsers.get(userId);
        member.roles.forEach(role => userData.roles.add(role));
        userData.requirements.push(currentVersion.title);
      }
    }

    // Check which users need to be added to the feature
    const usersToAdd = [];
    const existingFeatureUserIds = new Set(
      feature.members.map(member => member.user.toString())
    );

    for (const [userId, userData] of childRequirementUsers) {
      if (!existingFeatureUserIds.has(userId)) {
        usersToAdd.push({
          userId,
          roles: Array.from(userData.roles),
          requirements: userData.requirements
        });
      }
    }

    // Auto-add missing users if requested
    if (req.body.autoSync && usersToAdd.length > 0) {
      for (const userToAdd of usersToAdd) {
        feature.members.push({
          user: userToAdd.userId,
          roles: userToAdd.roles
        });
      }

      await feature.save();

      // Return updated feature with populated user data
      const populatedFeature = await Feature.findById(feature._id)
        .populate('createdBy', 'username color firstName lastName avatar')
        .populate('members.user', 'username color firstName lastName avatar');

      return res.json({
        synced: true,
        addedUsers: usersToAdd,
        feature: populatedFeature
      });
    }

    // Return validation results without syncing
    res.json({
      synced: false,
      missingUsers: usersToAdd,
      totalChildRequirements: childRequirements.length
    });

  } catch (err) {
    console.error('Error validating feature members:', err);
    res.status(500).send('Server Error');
  }
});

// Add release tag to feature (current version)
router.post('/:featureId/tags', auth, addGroupContext, async (req, res) => {
  try {
    const { tag } = req.body;

    if (!tag || !tag.trim()) {
      return res.status(400).json({ message: 'Tag is required' });
    }

    const feature = await Feature.findById(req.params.featureId);
    if (!feature) {
      return res.status(404).json({ message: 'Feature not found' });
    }

    // Check group access for non-super users
    if (!req.isSuperUser && feature.group.toString() !== req.userGroup._id.toString()) {
      return res.status(403).json({ message: 'Access denied' });
    }

    // Add tag to current version
    await feature.addReleaseTag(tag.trim(), req.user.userId);

    // Return the feature with populated user data
    const populatedFeature = await Feature.findById(feature._id)
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar')
      .populate('versions.releaseTags.addedBy', 'username firstName lastName');

    res.json(populatedFeature);
  } catch (err) {
    console.error('Error adding release tag to feature:', err);
    res.status(500).send('Server Error');
  }
});

// Add release tag to specific feature version
router.post('/:featureId/versions/:versionNumber/tags', auth, addGroupContext, async (req, res) => {
  try {
    const { tag } = req.body;
    const versionNumber = parseInt(req.params.versionNumber);

    if (!tag || !tag.trim()) {
      return res.status(400).json({ message: 'Tag is required' });
    }

    if (isNaN(versionNumber) || versionNumber < 1) {
      return res.status(400).json({ message: 'Invalid version number' });
    }

    const feature = await Feature.findById(req.params.featureId);
    if (!feature) {
      return res.status(404).json({ message: 'Feature not found' });
    }

    // Check group access for non-super users
    if (!req.isSuperUser && feature.group.toString() !== req.userGroup._id.toString()) {
      return res.status(403).json({ message: 'Access denied' });
    }

    // Add tag to specific version
    await feature.addReleaseTag(tag.trim(), req.user.userId, versionNumber);

    // Return the feature with populated user data
    const populatedFeature = await Feature.findById(feature._id)
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar')
      .populate('versions.releaseTags.addedBy', 'username firstName lastName');

    res.json(populatedFeature);
  } catch (err) {
    console.error('Error adding release tag to feature version:', err);
    if (err.message.includes('Version') && err.message.includes('not found')) {
      return res.status(404).json({ message: err.message });
    }
    res.status(500).send('Server Error');
  }
});

// Remove release tag from feature (current version)
router.delete('/:featureId/tags/:tag', auth, addGroupContext, async (req, res) => {
  try {
    const feature = await Feature.findById(req.params.featureId);
    if (!feature) {
      return res.status(404).json({ message: 'Feature not found' });
    }

    // Check group access for non-super users
    if (!req.isSuperUser && feature.group.toString() !== req.userGroup._id.toString()) {
      return res.status(403).json({ message: 'Access denied' });
    }

    // Remove tag from current version
    await feature.removeReleaseTag(req.params.tag);

    // Return the feature with populated user data
    const populatedFeature = await Feature.findById(feature._id)
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar')
      .populate('versions.releaseTags.addedBy', 'username firstName lastName');

    res.json(populatedFeature);
  } catch (err) {
    console.error('Error removing release tag from feature:', err);
    res.status(500).send('Server Error');
  }
});

// Remove release tag from specific feature version
router.delete('/:featureId/versions/:versionNumber/tags/:tag', auth, addGroupContext, async (req, res) => {
  try {
    const versionNumber = parseInt(req.params.versionNumber);

    if (isNaN(versionNumber) || versionNumber < 1) {
      return res.status(400).json({ message: 'Invalid version number' });
    }

    const feature = await Feature.findById(req.params.featureId);
    if (!feature) {
      return res.status(404).json({ message: 'Feature not found' });
    }

    // Check group access for non-super users
    if (!req.isSuperUser && feature.group.toString() !== req.userGroup._id.toString()) {
      return res.status(403).json({ message: 'Access denied' });
    }

    // Remove tag from specific version
    await feature.removeReleaseTag(req.params.tag, versionNumber);

    // Return the feature with populated user data
    const populatedFeature = await Feature.findById(feature._id)
      .populate('createdBy', 'username color firstName lastName avatar')
      .populate('members.user', 'username color firstName lastName avatar')
      .populate('versions.releaseTags.addedBy', 'username firstName lastName');

    res.json(populatedFeature);
  } catch (err) {
    console.error('Error removing release tag from feature version:', err);
    if (err.message.includes('Version') && err.message.includes('not found')) {
      return res.status(404).json({ message: err.message });
    }
    res.status(500).send('Server Error');
  }
});

// Bulk apply tag to feature and selected child requirements
router.post('/:featureId/bulk-tag', auth, addGroupContext, async (req, res) => {
  try {
    const { tag, childRequirementIds, applyToFeature = true } = req.body;

    if (!tag || !tag.trim()) {
      return res.status(400).json({ message: 'Tag is required' });
    }

    const feature = await Feature.findById(req.params.featureId);
    if (!feature) {
      return res.status(404).json({ message: 'Feature not found' });
    }

    // Check group access for non-super users
    if (!req.isSuperUser && feature.group.toString() !== req.userGroup._id.toString()) {
      return res.status(403).json({ message: 'Access denied' });
    }

    const results = {
      featureUpdated: false,
      requirementsUpdated: 0,
      errors: []
    };

    // Apply tag to feature current version if requested
    if (applyToFeature) {
      try {
        await feature.addReleaseTag(tag.trim(), req.user.userId);
        results.featureUpdated = true;
      } catch (err) {
        results.errors.push(`Feature: ${err.message}`);
      }
    }

    // Apply tag to selected child requirements (current versions)
    if (childRequirementIds && childRequirementIds.length > 0) {
      const Requirement = require('../models/Requirement');

      for (const reqId of childRequirementIds) {
        try {
          const requirement = await Requirement.findById(reqId);
          if (!requirement) {
            results.errors.push(`Requirement ${reqId}: not found`);
            continue;
          }

          // Check group access for non-super users
          if (!req.isSuperUser && requirement.group.toString() !== req.userGroup._id.toString()) {
            results.errors.push(`Requirement ${reqId}: access denied`);
            continue;
          }

          // Add tag to current version of requirement
          await requirement.addReleaseTag(tag.trim(), req.user.userId);
          results.requirementsUpdated++;
        } catch (err) {
          results.errors.push(`Requirement ${reqId}: ${err.message}`);
        }
      }
    }

    res.json(results);
  } catch (err) {
    console.error('Error bulk applying release tag:', err);
    res.status(500).send('Server Error');
  }
});

module.exports = router;