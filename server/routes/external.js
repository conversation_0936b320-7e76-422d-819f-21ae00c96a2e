const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const { addGroupContext } = require('../middleware/groupAuth');
const Requirement = require('../models/Requirement');
const Feature = require('../models/Feature');

// Debug logging middleware for external routes
router.use((req, res, next) => {
  console.log('External API route hit:', req.method, req.path);
  next();
});

// Add release tag to requirement via external API (Jenkins/GitHub)
router.post('/requirements/:requirementId/tags', auth, addGroupContext, async (req, res) => {
  try {
    const { tag } = req.body;
    
    if (!tag || !tag.trim()) {
      return res.status(400).json({ 
        success: false,
        message: 'Tag is required' 
      });
    }

    const requirement = await Requirement.findById(req.params.requirementId);
    if (!requirement) {
      return res.status(404).json({ 
        success: false,
        message: 'Requirement not found' 
      });
    }

    // Check group access for non-super users
    if (!req.isSuperUser && requirement.group.toString() !== req.userGroup._id.toString()) {
      return res.status(403).json({ 
        success: false,
        message: 'Access denied' 
      });
    }

    await requirement.addReleaseTag(tag.trim(), req.user.userId);

    res.json({
      success: true,
      message: 'Tag added successfully',
      requirementId: requirement._id,
      tag: tag.trim(),
      addedAt: new Date()
    });
  } catch (err) {
    console.error('Error adding release tag via external API:', err);
    res.status(500).json({
      success: false,
      message: 'Server Error'
    });
  }
});

// Add release tag to feature via external API (Jenkins/GitHub)
router.post('/features/:featureId/tags', auth, addGroupContext, async (req, res) => {
  try {
    const { tag } = req.body;
    
    if (!tag || !tag.trim()) {
      return res.status(400).json({ 
        success: false,
        message: 'Tag is required' 
      });
    }

    const feature = await Feature.findById(req.params.featureId);
    if (!feature) {
      return res.status(404).json({ 
        success: false,
        message: 'Feature not found' 
      });
    }

    // Check group access for non-super users
    if (!req.isSuperUser && feature.group.toString() !== req.userGroup._id.toString()) {
      return res.status(403).json({ 
        success: false,
        message: 'Access denied' 
      });
    }

    await feature.addReleaseTag(tag.trim(), req.user.userId);

    res.json({
      success: true,
      message: 'Tag added successfully',
      featureId: feature._id,
      tag: tag.trim(),
      addedAt: new Date()
    });
  } catch (err) {
    console.error('Error adding release tag to feature via external API:', err);
    res.status(500).json({
      success: false,
      message: 'Server Error'
    });
  }
});

// Bulk add tags to multiple requirements via external API
router.post('/requirements/bulk-tags', auth, addGroupContext, async (req, res) => {
  try {
    const { requirementIds, tag } = req.body;
    
    if (!tag || !tag.trim()) {
      return res.status(400).json({ 
        success: false,
        message: 'Tag is required' 
      });
    }

    if (!requirementIds || !Array.isArray(requirementIds) || requirementIds.length === 0) {
      return res.status(400).json({ 
        success: false,
        message: 'Requirement IDs array is required' 
      });
    }

    const results = {
      success: true,
      updated: 0,
      errors: []
    };

    for (const reqId of requirementIds) {
      try {
        const requirement = await Requirement.findById(reqId);
        if (!requirement) {
          results.errors.push(`Requirement ${reqId}: not found`);
          continue;
        }

        // Check group access for non-super users
        if (!req.isSuperUser && requirement.group.toString() !== req.userGroup._id.toString()) {
          results.errors.push(`Requirement ${reqId}: access denied`);
          continue;
        }

        await requirement.addReleaseTag(tag.trim(), req.user.userId);
        results.updated++;
      } catch (err) {
        results.errors.push(`Requirement ${reqId}: ${err.message}`);
      }
    }

    if (results.errors.length > 0 && results.updated === 0) {
      results.success = false;
    }

    res.json(results);
  } catch (err) {
    console.error('Error bulk adding release tags via external API:', err);
    res.status(500).json({
      success: false,
      message: 'Server Error'
    });
  }
});

module.exports = router;
