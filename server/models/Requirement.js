const mongoose = require('mongoose');

const taskSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  text: {
    type: String,
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'completed'],
    default: 'pending'
  },
  role: {
    type: String,
    enum: ['developer', 'product_manager', 'project_manager', 'tester'],
    required: false
  }
});

const commentSchema = new mongoose.Schema({
  text: {
    type: String,
    required: true
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  version: {
    type: Number,
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Schema for linked text data
const linkedTextSchema = new mongoose.Schema({
  id: {
    type: String,
    required: true
  },
  text: {
    type: String,
    required: true
  },
  commentId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true
  },
  startOffset: {
    type: Number,
    required: true
  },
  endOffset: {
    type: Number,
    required: true
  },
  highlightColor: {
    type: String,
    default: '#1976d2'
  },
  version: {
    type: Number,
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

const versionSchema = new mongoose.Schema({
  version: {
    type: Number,
    required: true
  },
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  state: {
    type: String,
    enum: ['New', 'Being Drafted', 'Requiring Approval', 'Ready for Dev', 'Being Built', 'Ready for Test', 'Under Test', 'Testing Satisfactory', 'Shipped/Deployed to Customer'],
    default: 'New'
  },
  labels: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Label'
  }],
  // Release tags for this specific version
  releaseTags: [{
    tag: {
      type: String,
      required: true
    },
    dateAdded: {
      type: Date,
      default: Date.now
    },
    addedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    _id: false // Prevent mongoose from adding _id to subdocs
  }],
  tasks: [taskSchema],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  changes: {
    type: [{
      field: String,
      from: String,
      to: String
    }],
    default: []
  },
  comments: [commentSchema],
  approvals: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    approvedAt: {
      type: Date,
      default: Date.now
    }
  }],

  // Document labels tracking which documents this version has appeared in
  documentLabels: [{
    documentId: {
      type: String,
      required: true
    },
    documentType: {
      type: String,
      enum: ['release_notes', 'srd'],
      required: true
    },
    generatedAt: {
      type: Date,
      required: true
    },
    title: {
      type: String,
      required: true
    },
    _id: false // Prevent mongoose from adding _id to subdocs
  }]
});

const requirementSchema = new mongoose.Schema({
  project: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Project',
    required: true
  },
  group: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Group',
    required: true
  },
  feature: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Feature'
  },
  type: {
    type: String,
    enum: ['user_story', 'requirement'],
    required: true,
    default: 'requirement'
  },
  versions: [versionSchema],
  currentVersion: {
    type: Number,
    default: 1
  },

  members: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    roles: [{
      type: String,
      enum: ['developer', 'product_manager', 'project_manager', 'tester'],
      required: true
    }]
  }],
  // Users who are designated as approvers (applies to all versions)
  approvers: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    addedInVersion: {
      type: Number,
      required: true
    }
  }],
  // Actual approvals given by users for specific versions
  approvals: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    version: {
      type: Number,
      required: true
    },
    approvedAt: {
      type: Date,
      default: Date.now
    }
  }],

  // Test Management - organized by requirement version
  testSuites: [{
    version: {
      type: Number,
      required: true
    },
    tests: [{
      id: {
        type: String,
        required: true
      },
      testName: String,
      setup: String,
      steps: String,
      expectedResult: String,
      results: [{
        date: Date,
        status: {
          type: String,
          enum: ['Pass', 'Fail', 'Skip', null],
          default: null
        },
        bugNumbers: [String], // For future bug tracking
        notes: String
      }]
    }],
    createdAt: {
      type: Date,
      default: Date.now
    },
    updatedAt: {
      type: Date,
      default: Date.now
    }
  }],

  // Comment linking data - stores text selections linked to comments
  linkedTexts: [linkedTextSchema],




  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Add indexes for performance
requirementSchema.index({ createdAt: -1 });
requirementSchema.index({ project: 1, createdAt: -1 });
requirementSchema.index({ feature: 1, createdAt: -1 });
requirementSchema.index({ type: 1, createdAt: -1 });
requirementSchema.index({ 'members.user': 1 });
requirementSchema.index({ 'approvers.user': 1 });
requirementSchema.index({ currentVersion: 1 });
requirementSchema.index({ project: 1, type: 1, createdAt: -1 });

// Method to add a new version
requirementSchema.methods.addVersion = async function(versionData) {
  this.currentVersion += 1;
  versionData.version = this.currentVersion;
  this.versions.push(versionData);
  return this.save();
};

// Method to get a specific version
requirementSchema.methods.getVersion = function(versionNumber) {
  return this.versions.find(v => v.version === versionNumber);
};

// Method to add a child requirement
requirementSchema.methods.addChild = async function(childId) {
  this.children.push(childId);
  return this.save();
};

// Method to get valid next/previous states for a requirement version
requirementSchema.statics.getValidTransitions = function(currentState) {
  const stateOrder = [
    'New',
    'Being Drafted',
    'Requiring Approval',
    'Ready for Dev',
    'Being Built',
    'Ready for Test',
    'Under Test',
    'Testing Satisfactory',
    'Shipped/Deployed to Customer'
  ];

  const currentIndex = stateOrder.indexOf(currentState);
  if (currentIndex === -1) return { next: null, previous: null };

  return {
    next: currentIndex < stateOrder.length - 1 ? stateOrder[currentIndex + 1] : null,
    previous: currentIndex > 0 ? stateOrder[currentIndex - 1] : null
  };
};

// Method to validate state transition for a specific version
requirementSchema.methods.canTransitionState = function(versionNumber, newState) {
  const version = this.versions.find(v => v.version === versionNumber);
  if (!version) {
    return { canTransition: false, reason: 'Version not found' };
  }

  const validTransitions = this.constructor.getValidTransitions(version.state);

  // Check if the new state is a valid next or previous state
  if (newState !== validTransitions.next && newState !== validTransitions.previous) {
    return {
      canTransition: false,
      reason: `Can only transition to ${validTransitions.next || 'none'} (next) or ${validTransitions.previous || 'none'} (previous) from ${version.state}`
    };
  }

  // Special validation for "Ready for Dev" - requires all approvers to have approved
  if (newState === 'Ready for Dev') {
    const approvers = this.approvers.filter(approver => approver.addedInVersion <= versionNumber);
    if (approvers.length > 0) {
      const approvals = this.approvals.filter(approval =>
        approval.version === versionNumber &&
        approvers.some(approver => approver.user.toString() === approval.user.toString())
      );

      if (approvals.length < approvers.length) {
        return {
          canTransition: false,
          reason: 'All approvers must approve before moving to Ready for Dev'
        };
      }
    }
  }

  return { canTransition: true, reason: null };
};

// Method to transition state for a specific version
requirementSchema.methods.transitionState = async function(versionNumber, newState) {
  const validation = this.canTransitionState(versionNumber, newState);
  if (!validation.canTransition) {
    throw new Error(validation.reason);
  }

  const version = this.versions.find(v => v.version === versionNumber);
  const oldState = version.state;
  version.state = newState;

  // Track the state change
  if (!version.changes) {
    version.changes = [];
  }
  version.changes.push({
    field: 'state',
    from: oldState,
    to: newState
  });

  return this.save();
};

// Method to add a release tag to a specific version
requirementSchema.methods.addReleaseTag = function(tag, userId, versionNumber = null) {
  // Default to current version if no version specified
  const targetVersion = versionNumber || this.currentVersion;

  // Find the version
  const version = this.versions.find(v => v.version === targetVersion);
  if (!version) {
    throw new Error(`Version ${targetVersion} not found`);
  }

  // Check if tag already exists in this version
  const existingTag = version.releaseTags.find(t => t.tag === tag);
  if (existingTag) {
    // Make tag unique with timestamp
    tag = `${tag}-${Date.now()}`;
  }

  version.releaseTags.push({
    tag: tag,
    addedBy: userId,
    dateAdded: new Date()
  });

  return this.save();
};

// Method to remove a release tag from a specific version
requirementSchema.methods.removeReleaseTag = function(tag, versionNumber = null) {
  // Default to current version if no version specified
  const targetVersion = versionNumber || this.currentVersion;

  // Find the version
  const version = this.versions.find(v => v.version === targetVersion);
  if (!version) {
    throw new Error(`Version ${targetVersion} not found`);
  }

  version.releaseTags = version.releaseTags.filter(t => t.tag !== tag);
  return this.save();
};

// Method to get all tags for a specific version
requirementSchema.methods.getVersionTags = function(versionNumber = null) {
  const targetVersion = versionNumber || this.currentVersion;
  const version = this.versions.find(v => v.version === targetVersion);
  return version ? version.releaseTags : [];
};

// Update the updatedAt timestamp before saving
requirementSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

const Requirement = mongoose.model('Requirement', requirementSchema);

module.exports = Requirement;