const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const dotenv = require('dotenv');
const auth = require('./middleware/auth');

// Load environment variables
dotenv.config();

// Import routes
const userRoutes = require('./routes/users');
const projectRoutes = require('./routes/projects');
const featureRoutes = require('./routes/features');
const requirementRoutes = require('./routes/requirements');
const labelRoutes = require('./routes/labels');
const authRoutes = require('./routes/auth');
const groupRoutes = require('./routes/groups');
const adminRoutes = require('./routes/admin');
const documentsRoutes = require('./routes/documents');

// Initialize Express app
const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Connect to MongoDB only if not in test environment
if (process.env.NODE_ENV !== 'test') {
  mongoose.connect(process.env.MONGODB_URI)
    .then(() => console.log('Connected to MongoDB'))
    .catch(err => console.error('Could not connect to MongoDB:', err));
}

// Routes
app.use('/api/users', userRoutes);
app.use('/api/projects', projectRoutes);
app.use('/api/features', featureRoutes);
app.use('/api/requirements', requirementRoutes);
app.use('/api/labels', labelRoutes);
app.use('/api/auth', authRoutes);
app.use('/api/groups', groupRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/documents', documentsRoutes);

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ message: 'Something went wrong!' });
});

// Export app for testing
module.exports = app;

// Start server only if not imported as module
if (require.main === module) {
  const PORT = process.env.PORT || 5000;
  app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
  });
}