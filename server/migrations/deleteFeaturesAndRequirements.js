const mongoose = require('mongoose');
const Feature = require('../models/Feature');
const Requirement = require('../models/Requirement');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../../.env') });

const deleteFeaturesAndRequirements = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Delete all requirements first (they may reference features)
    const requirementResult = await Requirement.deleteMany({});
    console.log(`Deleted ${requirementResult.deletedCount} requirements`);

    // Delete all features
    const featureResult = await Feature.deleteMany({});
    console.log(`Deleted ${featureResult.deletedCount} features`);

    console.log('Deletion completed successfully');
    console.log('Total deleted:', {
      requirements: requirementResult.deletedCount,
      features: featureResult.deletedCount
    });
  } catch (error) {
    console.error('Deletion failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
};

deleteFeaturesAndRequirements();
