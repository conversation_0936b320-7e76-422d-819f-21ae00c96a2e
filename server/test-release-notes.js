const axios = require('axios');

// Test script to verify release notes functionality
const BASE_URL = 'http://localhost:5000/api';

// Test user credentials (using existing user)
const testUser = {
  username: 'rosyman',
  password: 'ttttt'
};

let authToken = '';
let projectId = '';
let featureId = '';
let requirementId = '';

const login = async () => {
  try {
    console.log('🔐 Logging in...');
    const response = await axios.post(`${BASE_URL}/auth/login`, testUser);
    authToken = response.data.token;
    console.log('✅ Login successful');
    return response.data;
  } catch (error) {
    console.error('❌ Login failed:', error.response?.data || error.message);
    throw error;
  }
};

const getAuthHeaders = () => ({
  'Authorization': `Bearer ${authToken}`,
  'Content-Type': 'application/json'
});

const createTestProject = async () => {
  try {
    console.log('📁 Creating test project...');
    const response = await axios.post(`${BASE_URL}/projects`, {
      name: 'Release Notes Test Project',
      description: 'A test project for release notes functionality',
      addSelf: true
    }, { headers: getAuthHeaders() });
    
    projectId = response.data._id;
    console.log('✅ Test project created:', projectId);
    return response.data;
  } catch (error) {
    console.error('❌ Project creation failed:', error.response?.data || error.message);
    throw error;
  }
};

const createTestFeature = async () => {
  try {
    console.log('🎯 Creating test feature...');
    const response = await axios.post(`${BASE_URL}/features`, {
      project: projectId,
      title: 'User Authentication System',
      description: 'Complete user authentication and authorization system'
    }, { headers: getAuthHeaders() });
    
    featureId = response.data._id;
    console.log('✅ Test feature created:', featureId);
    return response.data;
  } catch (error) {
    console.error('❌ Feature creation failed:', error.response?.data || error.message);
    throw error;
  }
};

const createTestRequirement = async () => {
  try {
    console.log('📋 Creating test requirement...');
    const response = await axios.post(`${BASE_URL}/requirements`, {
      project: projectId,
      feature: featureId,
      type: 'requirement',
      title: 'User Login Functionality',
      description: 'Users should be able to log in with username and password'
    }, { headers: getAuthHeaders() });
    
    requirementId = response.data._id;
    console.log('✅ Test requirement created:', requirementId);
    return response.data;
  } catch (error) {
    console.error('❌ Requirement creation failed:', error.response?.data || error.message);
    throw error;
  }
};

const addTagsToFeature = async () => {
  try {
    console.log('🏷️  Adding tags to feature...');
    
    // Add multiple tags
    const tags = ['v1.0.0', 'Release-2024-01', 'Sprint-15'];
    
    for (const tag of tags) {
      await axios.post(`${BASE_URL}/features/${featureId}/tags`, {
        tag: tag
      }, { headers: getAuthHeaders() });
      console.log(`✅ Added tag "${tag}" to feature`);
    }
  } catch (error) {
    console.error('❌ Adding tags to feature failed:', error.response?.data || error.message);
    throw error;
  }
};

const addTagsToRequirement = async () => {
  try {
    console.log('🏷️  Adding tags to requirement...');
    
    // Add multiple tags
    const tags = ['v1.0.0', 'Sprint-15', 'Critical'];
    
    for (const tag of tags) {
      await axios.post(`${BASE_URL}/requirements/${requirementId}/tags`, {
        tag: tag
      }, { headers: getAuthHeaders() });
      console.log(`✅ Added tag "${tag}" to requirement`);
    }
  } catch (error) {
    console.error('❌ Adding tags to requirement failed:', error.response?.data || error.message);
    throw error;
  }
};

const testReleaseNotesGeneration = async () => {
  try {
    console.log('📝 Testing release notes generation...');
    
    const response = await axios.post(`${BASE_URL}/release-notes/projects/${projectId}/release-notes`, {
      title: 'Test Release v1.0.0',
      filters: {
        tags: ['v1.0.0'],
        includeFeatures: true,
        includeRequirements: true,
        groupByFeature: true
      },
      save: true
    }, { headers: getAuthHeaders() });
    
    console.log('✅ Release notes generated successfully!');
    console.log('📄 Generated content preview:');
    console.log(response.data.releaseNotes.substring(0, 500) + '...');
    console.log('📊 Statistics:', response.data.stats);
    
    return response.data;
  } catch (error) {
    console.error('❌ Release notes generation failed:', error.response?.data || error.message);
    throw error;
  }
};

const getReleaseData = async () => {
  try {
    console.log('📊 Getting available release data...');
    
    const response = await axios.get(`${BASE_URL}/release-notes/projects/${projectId}/release-data`, {
      headers: getAuthHeaders()
    });
    
    console.log('✅ Available tags:', response.data.tags);
    console.log('✅ Available labels:', response.data.labels.map(l => l.name));
    
    return response.data;
  } catch (error) {
    console.error('❌ Getting release data failed:', error.response?.data || error.message);
    throw error;
  }
};

const getSavedReleaseNotes = async () => {
  try {
    console.log('📚 Getting saved release notes...');
    
    const response = await axios.get(`${BASE_URL}/release-notes/projects/${projectId}/release-notes`, {
      headers: getAuthHeaders()
    });
    
    console.log('✅ Saved release notes count:', response.data.length);
    if (response.data.length > 0) {
      console.log('📄 Latest release notes:', response.data[0].title);
    }
    
    return response.data;
  } catch (error) {
    console.error('❌ Getting saved release notes failed:', error.response?.data || error.message);
    throw error;
  }
};

const runTest = async () => {
  try {
    console.log('🚀 Starting Release Notes Test Suite...\n');
    
    // Step 1: Login
    await login();
    
    // Step 2: Create test data
    await createTestProject();
    await createTestFeature();
    await createTestRequirement();
    
    // Step 3: Add tags
    await addTagsToFeature();
    await addTagsToRequirement();
    
    // Step 4: Test release notes functionality
    await getReleaseData();
    await testReleaseNotesGeneration();
    await getSavedReleaseNotes();
    
    console.log('\n🎉 All tests passed! Release notes functionality is working correctly.');
    console.log(`\n🌐 You can now test the UI at: http://localhost:3000/project/${projectId}/release-notes`);
    
  } catch (error) {
    console.error('\n💥 Test suite failed:', error.message);
    process.exit(1);
  }
};

// Run the test
runTest();
