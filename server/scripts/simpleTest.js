const { generateElementId, validateElementId, parseElementId } = require('../utils/elementIdGenerator');

console.log('🧪 Testing ElementID utilities...\n');

// Test validation
console.log('📝 Testing validation:');
const testIds = ['PROJ-001', 'ACME-F-001', 'TEST-R-123', 'invalid-id'];
testIds.forEach(id => {
  const valid = validateElementId(id);
  console.log(`  ${id}: ${valid ? '✅' : '❌'}`);
});

// Test parsing
console.log('\n📝 Testing parsing:');
const validIds = ['PROJ-001', 'ACME-F-001', 'TEST-R-123'];
validIds.forEach(id => {
  try {
    const parsed = parseElementId(id);
    console.log(`  ${id}: ${JSON.stringify(parsed)}`);
  } catch (error) {
    console.log(`  ${id}: ❌ ${error.message}`);
  }
});

console.log('\n✅ ElementID utilities test completed!');
