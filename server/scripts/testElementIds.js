const mongoose = require('mongoose');
const dotenv = require('dotenv');
const axios = require('axios');

// Load environment variables
dotenv.config();

// Import models and utilities
const { generateElementId, validateElementId, parseElementId } = require('../utils/elementIdGenerator');
const Project = require('../models/Project');
const Feature = require('../models/Feature');
const Requirement = require('../models/Requirement');
const User = require('../models/User');
const Group = require('../models/Group');

/**
 * Test script to verify ElementID functionality
 */
async function testElementIds() {
  try {
    console.log('🧪 Starting ElementID tests...\n');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB\n');

    // Test 1: ElementID Generation
    console.log('📝 Test 1: ElementID Generation');
    await testElementIdGeneration();
    
    // Test 2: ElementID Validation
    console.log('\n📝 Test 2: ElementID Validation');
    testElementIdValidation();
    
    // Test 3: ElementID Parsing
    console.log('\n📝 Test 3: ElementID Parsing');
    testElementIdParsing();
    
    // Test 4: Database Operations
    console.log('\n📝 Test 4: Database Operations');
    await testDatabaseOperations();
    
    // Test 5: API Endpoints (if server is running)
    console.log('\n📝 Test 5: API Endpoints');
    await testApiEndpoints();
    
    console.log('\n🎉 All tests completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
}

async function testElementIdGeneration() {
  try {
    // Find a test project
    const project = await Project.findOne({});
    if (!project) {
      console.log('⚠️  No projects found, skipping generation test');
      return;
    }
    
    // Test project ElementID generation
    const projectElementId = await generateElementId(project.group, project._id, 'project');
    console.log(`  ✅ Project ElementID: ${projectElementId}`);
    
    // Test feature ElementID generation
    const featureElementId = await generateElementId(project.group, project._id, 'feature');
    console.log(`  ✅ Feature ElementID: ${featureElementId}`);
    
    // Test requirement ElementID generation
    const requirementElementId = await generateElementId(project.group, project._id, 'requirement');
    console.log(`  ✅ Requirement ElementID: ${requirementElementId}`);
    
    // Test sequential generation
    const req1 = await generateElementId(project.group, project._id, 'requirement');
    const req2 = await generateElementId(project.group, project._id, 'requirement');
    console.log(`  ✅ Sequential generation: ${req1}, ${req2}`);
    
  } catch (error) {
    console.error('  ❌ Generation test failed:', error.message);
  }
}

function testElementIdValidation() {
  const testCases = [
    { id: 'PROJ-001', valid: true },
    { id: 'ACME-F-001', valid: true },
    { id: 'TEST-R-123', valid: true },
    { id: 'PROJ-1000', valid: true },
    { id: 'invalid-id', valid: false },
    { id: 'PROJ', valid: false },
    { id: '123-PROJ', valid: false },
    { id: '', valid: false },
    { id: null, valid: false }
  ];
  
  testCases.forEach(testCase => {
    const result = validateElementId(testCase.id);
    const status = result === testCase.valid ? '✅' : '❌';
    console.log(`  ${status} "${testCase.id}" -> ${result} (expected: ${testCase.valid})`);
  });
}

function testElementIdParsing() {
  const testCases = [
    'PROJ-001',
    'ACME-F-001',
    'TEST-R-123'
  ];
  
  testCases.forEach(elementId => {
    try {
      const parsed = parseElementId(elementId);
      console.log(`  ✅ "${elementId}" -> ${JSON.stringify(parsed)}`);
    } catch (error) {
      console.log(`  ❌ "${elementId}" -> Error: ${error.message}`);
    }
  });
}

async function testDatabaseOperations() {
  try {
    // Test finding elements by ElementID
    const projectWithElementId = await Project.findOne({ elementId: { $exists: true } });
    if (projectWithElementId) {
      console.log(`  ✅ Found project by ElementID: ${projectWithElementId.elementId}`);
    }
    
    const featureWithElementId = await Feature.findOne({ elementId: { $exists: true } });
    if (featureWithElementId) {
      console.log(`  ✅ Found feature by ElementID: ${featureWithElementId.elementId}`);
    }
    
    const requirementWithElementId = await Requirement.findOne({ elementId: { $exists: true } });
    if (requirementWithElementId) {
      console.log(`  ✅ Found requirement by ElementID: ${requirementWithElementId.elementId}`);
    }
    
    // Test uniqueness constraints
    const duplicateElementIds = await Project.aggregate([
      { $group: { _id: '$elementId', count: { $sum: 1 } } },
      { $match: { count: { $gt: 1 } } }
    ]);
    
    if (duplicateElementIds.length === 0) {
      console.log('  ✅ No duplicate ElementIDs found');
    } else {
      console.log(`  ❌ Found ${duplicateElementIds.length} duplicate ElementIDs`);
    }
    
  } catch (error) {
    console.error('  ❌ Database operations test failed:', error.message);
  }
}

async function testApiEndpoints() {
  try {
    const baseUrl = process.env.API_BASE_URL || 'http://localhost:5000';
    
    // Test if server is running
    try {
      await axios.get(`${baseUrl}/api/projects`);
      console.log('  ⚠️  API endpoint testing requires authentication setup');
      console.log('  💡 Manually test these endpoints:');
      console.log('    - GET /api/projects/:elementId');
      console.log('    - GET /api/features/:elementId');
      console.log('    - GET /api/requirements/:elementId');
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log('  ⚠️  Server not running, skipping API tests');
      } else {
        console.log('  ⚠️  API tests require authentication, skipping');
      }
    }
    
  } catch (error) {
    console.error('  ❌ API endpoint test failed:', error.message);
  }
}

// Run tests
testElementIds();
