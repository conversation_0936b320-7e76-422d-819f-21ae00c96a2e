const mongoose = require('mongoose');
const dotenv = require('dotenv');
const { generateElementId } = require('../utils/elementIdGenerator');

// Load environment variables
dotenv.config();

// Import models
const Project = require('../models/Project');
const Feature = require('../models/Feature');
const Requirement = require('../models/Requirement');
const Counter = require('../models/Counter');

/**
 * Migration script to add ElementIDs to existing data
 * This script should be run once when implementing the hybrid ElementID system
 */
async function migrateToElementIds() {
  try {
    console.log('Starting ElementID migration...');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('Connected to MongoDB');

    // Get all projects
    const projects = await Project.find({}).sort({ createdAt: 1 });
    console.log(`Found ${projects.length} projects to migrate`);

    for (const project of projects) {
      if (!project.elementId) {
        try {
          console.log(`Migrating project: ${project.name}`);
          
          // Generate ElementID for project
          const elementId = await generateElementId(project.group, project._id, 'project');
          project.elementId = elementId;
          await project.save();
          
          console.log(`  ✓ Project ElementID: ${elementId}`);
        } catch (error) {
          console.error(`  ✗ Error migrating project ${project.name}:`, error.message);
        }
      } else {
        console.log(`  ⏭ Project ${project.name} already has ElementID: ${project.elementId}`);
      }
    }

    // Get all features
    const features = await Feature.find({}).sort({ createdAt: 1 });
    console.log(`Found ${features.length} features to migrate`);

    for (const feature of features) {
      if (!feature.elementId) {
        try {
          const currentVersion = feature.versions[feature.versions.length - 1];
          console.log(`Migrating feature: ${currentVersion.title}`);
          
          // Generate ElementID for feature
          const elementId = await generateElementId(feature.group, feature.project, 'feature');
          feature.elementId = elementId;
          await feature.save();
          
          console.log(`  ✓ Feature ElementID: ${elementId}`);
        } catch (error) {
          console.error(`  ✗ Error migrating feature:`, error.message);
        }
      } else {
        const currentVersion = feature.versions[feature.versions.length - 1];
        console.log(`  ⏭ Feature ${currentVersion.title} already has ElementID: ${feature.elementId}`);
      }
    }

    // Get all requirements
    const requirements = await Requirement.find({}).sort({ createdAt: 1 });
    console.log(`Found ${requirements.length} requirements to migrate`);

    for (const requirement of requirements) {
      if (!requirement.elementId) {
        try {
          const currentVersion = requirement.versions[requirement.versions.length - 1];
          console.log(`Migrating requirement: ${currentVersion.title}`);
          
          // Generate ElementID for requirement
          const elementId = await generateElementId(requirement.group, requirement.project, 'requirement');
          requirement.elementId = elementId;
          await requirement.save();
          
          console.log(`  ✓ Requirement ElementID: ${elementId}`);
        } catch (error) {
          console.error(`  ✗ Error migrating requirement:`, error.message);
        }
      } else {
        const currentVersion = requirement.versions[requirement.versions.length - 1];
        console.log(`  ⏭ Requirement ${currentVersion.title} already has ElementID: ${requirement.elementId}`);
      }
    }

    console.log('\n✅ Migration completed successfully!');
    
    // Print summary
    const projectsWithElementId = await Project.countDocuments({ elementId: { $exists: true, $ne: null } });
    const featuresWithElementId = await Feature.countDocuments({ elementId: { $exists: true, $ne: null } });
    const requirementsWithElementId = await Requirement.countDocuments({ elementId: { $exists: true, $ne: null } });
    
    console.log('\n📊 Migration Summary:');
    console.log(`Projects with ElementID: ${projectsWithElementId}/${projects.length}`);
    console.log(`Features with ElementID: ${featuresWithElementId}/${features.length}`);
    console.log(`Requirements with ElementID: ${requirementsWithElementId}/${requirements.length}`);
    
    // Show some examples
    console.log('\n🔍 Sample ElementIDs:');
    const sampleProject = await Project.findOne({ elementId: { $exists: true } });
    const sampleFeature = await Feature.findOne({ elementId: { $exists: true } });
    const sampleRequirement = await Requirement.findOne({ elementId: { $exists: true } });
    
    if (sampleProject) console.log(`Project: ${sampleProject.elementId} - ${sampleProject.name}`);
    if (sampleFeature) {
      const version = sampleFeature.versions[sampleFeature.versions.length - 1];
      console.log(`Feature: ${sampleFeature.elementId} - ${version.title}`);
    }
    if (sampleRequirement) {
      const version = sampleRequirement.versions[sampleRequirement.versions.length - 1];
      console.log(`Requirement: ${sampleRequirement.elementId} - ${version.title}`);
    }

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
}

/**
 * Rollback function to remove ElementIDs (for testing purposes)
 */
async function rollbackElementIds() {
  try {
    console.log('Starting ElementID rollback...');
    
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    // Remove ElementIDs from all collections
    await Project.updateMany({}, { $unset: { elementId: 1 } });
    await Feature.updateMany({}, { $unset: { elementId: 1 } });
    await Requirement.updateMany({}, { $unset: { elementId: 1 } });
    
    // Clear counters
    await Counter.deleteMany({});
    
    console.log('✅ Rollback completed successfully!');
    
  } catch (error) {
    console.error('❌ Rollback failed:', error);
  } finally {
    await mongoose.disconnect();
  }
}

// Run migration or rollback based on command line argument
const command = process.argv[2];

if (command === 'rollback') {
  rollbackElementIds();
} else {
  migrateToElementIds();
}
