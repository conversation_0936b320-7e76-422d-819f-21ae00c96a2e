const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Import models
const Project = require('../models/Project');
const Feature = require('../models/Feature');
const Requirement = require('../models/Requirement');

async function checkDatabase() {
  try {
    console.log('🔍 Checking database connection and data...');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
    console.log('Database:', mongoose.connection.name);

    // Check existing data
    const projectCount = await Project.countDocuments();
    const featureCount = await Feature.countDocuments();
    const requirementCount = await Requirement.countDocuments();
    
    console.log('\n📊 Current Data:');
    console.log(`Projects: ${projectCount}`);
    console.log(`Features: ${featureCount}`);
    console.log(`Requirements: ${requirementCount}`);
    
    // Check if any elements already have ElementIDs
    const projectsWithElementId = await Project.countDocuments({ elementId: { $exists: true, $ne: null } });
    const featuresWithElementId = await Feature.countDocuments({ elementId: { $exists: true, $ne: null } });
    const requirementsWithElementId = await Requirement.countDocuments({ elementId: { $exists: true, $ne: null } });
    
    console.log('\n🏷️  Elements with ElementIDs:');
    console.log(`Projects: ${projectsWithElementId}/${projectCount}`);
    console.log(`Features: ${featuresWithElementId}/${featureCount}`);
    console.log(`Requirements: ${requirementsWithElementId}/${requirementCount}`);
    
    // Show sample data
    if (projectCount > 0) {
      console.log('\n📝 Sample Projects:');
      const sampleProjects = await Project.find({}).limit(3).select('name elementId createdAt');
      sampleProjects.forEach(project => {
        console.log(`  - ${project.name} (ElementID: ${project.elementId || 'None'}) - Created: ${project.createdAt}`);
      });
    }
    
    if (featureCount > 0) {
      console.log('\n📝 Sample Features:');
      const sampleFeatures = await Feature.find({}).limit(3).select('elementId versions.title createdAt');
      sampleFeatures.forEach(feature => {
        const title = feature.versions[feature.versions.length - 1]?.title || 'No title';
        console.log(`  - ${title} (ElementID: ${feature.elementId || 'None'}) - Created: ${feature.createdAt}`);
      });
    }
    
    if (requirementCount > 0) {
      console.log('\n📝 Sample Requirements:');
      const sampleRequirements = await Requirement.find({}).limit(3).select('elementId versions.title createdAt');
      sampleRequirements.forEach(requirement => {
        const title = requirement.versions[requirement.versions.length - 1]?.title || 'No title';
        console.log(`  - ${title} (ElementID: ${requirement.elementId || 'None'}) - Created: ${requirement.createdAt}`);
      });
    }
    
    if (projectCount === 0 && featureCount === 0 && requirementCount === 0) {
      console.log('\n💡 No data found. You may need to:');
      console.log('   1. Create some test data first');
      console.log('   2. Check if you\'re connected to the correct database');
      console.log('   3. Verify the MONGODB_URI in your .env file');
    }
    
  } catch (error) {
    console.error('❌ Database check failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

checkDatabase();
