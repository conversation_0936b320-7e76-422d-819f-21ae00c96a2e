const mongoose = require('mongoose');
const { validateElementId } = require('../utils/elementIdGenerator');

/**
 * Middleware to resolve ElementID to ObjectID
 * Supports both ElementID and ObjectID for backward compatibility
 * @param {string} modelName - The model name ('Project', 'Feature', 'Requirement')
 * @returns {Function} - Express middleware function
 */
function createElementIdResolver(modelName) {
  return async (req, res, next) => {
    try {
      const { id } = req.params;
      
      if (!id) {
        return res.status(400).json({ message: 'ID parameter is required' });
      }
      
      // Check if it's a valid ObjectId (backward compatibility)
      if (mongoose.Types.ObjectId.isValid(id) && id.length === 24) {
        req.resolvedId = id;
        req.idType = 'objectId';
        return next();
      }
      
      // Check if it's a valid ElementID format
      if (!validateElementId(id)) {
        return res.status(400).json({ message: 'Invalid ID format' });
      }
      
      // Get the model
      const Model = mongoose.model(modelName);
      
      // Build query filter
      const filter = { elementId: id };
      
      // Add group filter for multi-tenancy (if user context available)
      if (req.user && req.user.group) {
        filter.group = req.user.group;
      } else if (req.userGroup) {
        filter.group = req.userGroup._id;
      }
      
      // Find the element by ElementID
      const element = await Model.findOne(filter).select('_id');
      
      if (!element) {
        return res.status(404).json({ message: `${modelName} not found` });
      }
      
      // Store resolved ObjectID for use in route handlers
      req.resolvedId = element._id.toString();
      req.idType = 'elementId';
      req.originalElementId = id;
      
      next();
    } catch (error) {
      console.error('ElementID resolution error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  };
}

/**
 * Specific resolvers for each model type
 */
const resolveProjectId = createElementIdResolver('Project');
const resolveFeatureId = createElementIdResolver('Feature');
const resolveRequirementId = createElementIdResolver('Requirement');

/**
 * Resolver for requirementId parameter specifically
 */
const resolveRequirementIdParam = async (req, res, next) => {
  try {
    const { requirementId } = req.params;

    if (!requirementId) {
      return res.status(400).json({ message: 'RequirementId parameter is required' });
    }

    // Check if it's a valid ObjectId (backward compatibility)
    if (mongoose.Types.ObjectId.isValid(requirementId) && requirementId.length === 24) {
      req.resolvedRequirementId = requirementId;
      req.idType = 'objectId';
      return next();
    }

    // Check if it's a valid ElementID format
    if (!validateElementId(requirementId)) {
      return res.status(400).json({ message: 'Invalid RequirementId format' });
    }

    // Get the Requirement model
    const Requirement = mongoose.model('Requirement');

    // Build query filter
    const filter = { elementId: requirementId };

    // Add group filter for multi-tenancy (if user context available)
    if (req.user && req.user.group) {
      filter.group = req.user.group;
    } else if (req.userGroup) {
      filter.group = req.userGroup._id;
    }

    // Find the requirement by ElementID
    const requirement = await Requirement.findOne(filter).select('_id');

    if (!requirement) {
      return res.status(404).json({ message: 'Requirement not found' });
    }

    req.resolvedRequirementId = requirement._id;
    req.idType = 'elementId';
    next();
  } catch (error) {
    console.error('Error resolving RequirementId:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

/**
 * Generic resolver that can handle any model type
 * @param {string} modelName - The model name
 * @param {string} paramName - The parameter name (default: 'id')
 */
function resolveElementId(modelName, paramName = 'id') {
  return async (req, res, next) => {
    try {
      const id = req.params[paramName];
      
      if (!id) {
        return res.status(400).json({ message: `${paramName} parameter is required` });
      }
      
      // Check if it's a valid ObjectId (backward compatibility)
      if (mongoose.Types.ObjectId.isValid(id) && id.length === 24) {
        req[`resolved${paramName.charAt(0).toUpperCase() + paramName.slice(1)}`] = id;
        return next();
      }
      
      // Check if it's a valid ElementID format
      if (!validateElementId(id)) {
        return res.status(400).json({ message: `Invalid ${paramName} format` });
      }
      
      // Get the model
      const Model = mongoose.model(modelName);
      
      // Build query filter
      const filter = { elementId: id };
      
      // Add group filter for multi-tenancy
      if (req.user && req.user.group) {
        filter.group = req.user.group;
      } else if (req.userGroup) {
        filter.group = req.userGroup._id;
      }
      
      // Find the element by ElementID
      const element = await Model.findOne(filter).select('_id');
      
      if (!element) {
        return res.status(404).json({ message: `${modelName} not found` });
      }
      
      // Store resolved ObjectID
      req[`resolved${paramName.charAt(0).toUpperCase() + paramName.slice(1)}`] = element._id.toString();
      
      next();
    } catch (error) {
      console.error(`${modelName} ID resolution error:`, error);
      res.status(500).json({ message: 'Internal server error' });
    }
  };
}

module.exports = {
  createElementIdResolver,
  resolveProjectId,
  resolveFeatureId,
  resolveRequirementId,
  resolveRequirementIdParam,
  resolveElementId
};
