const jwt = require('jsonwebtoken');
const User = require('../../models/User');
const Group = require('../../models/Group');
const GroupMembership = require('../../models/GroupMembership');

/**
 * Create a test group with specified tier
 */
async function createTestGroup(name, code, tier = '1-3', createdBy) {
  const group = new Group({
    name,
    code,
    tier,
    maxUsers: Group.getMaxUsersForTier(tier),
    createdBy: createdBy._id,
    status: 'active'
  });
  return await group.save();
}

/**
 * Create a test user
 */
async function createTestUser(userData, group) {
  const user = new User({
    username: userData.username,
    email: userData.email,
    firstName: userData.firstName || 'Test',
    lastName: userData.lastName || 'User',
    password: 'password123',
    group: group._id,
    groupStatus: userData.groupStatus || 'active',
    isSuperUser: userData.isSuperUser || false
  });
  return await user.save();
}

/**
 * Create a group membership
 */
async function createGroupMembership(group, user, role = 'member', status = 'active') {
  const membership = new GroupMembership({
    group: group._id,
    user: user._id,
    role,
    status,
    invitedBy: user._id // Self-invited for test purposes
  });
  return await membership.save();
}

/**
 * Generate JWT token for user
 */
function generateAuthToken(user) {
  return jwt.sign(
    { userId: user._id },
    process.env.JWT_SECRET || 'test-secret',
    { expiresIn: '1h' }
  );
}

/**
 * Create a complete test setup with group, admin user, and auth token
 */
async function createTestSetup(groupData = {}) {
  const mongoose = require('mongoose');

  // Create a temporary group first for super user
  const tempGroup = new Group({
    name: 'Temp Group',
    code: 'temp-group',
    tier: '1-3',
    maxUsers: 3,
    createdBy: new mongoose.Types.ObjectId() // Temporary ObjectId
  });
  await tempGroup.save();

  // Create super user
  const superUser = await createTestUser({
    username: 'superuser',
    email: '<EMAIL>',
    firstName: 'Super',
    lastName: 'User',
    isSuperUser: true,
    groupStatus: 'active'
  }, tempGroup);

  // Update temp group with super user as creator
  tempGroup.createdBy = superUser._id;
  await tempGroup.save();

  // Create actual test group
  const group = await createTestGroup(
    groupData.name || 'Test Group',
    groupData.code || 'test-group',
    groupData.tier || '1-3',
    superUser
  );

  // Create admin user
  const adminUser = await createTestUser({
    username: groupData.adminUsername || 'admin',
    email: groupData.adminEmail || '<EMAIL>',
    firstName: 'Admin',
    lastName: 'User',
    groupStatus: 'active'
  }, group);

  // Create admin membership
  await createGroupMembership(group, adminUser, 'administrator', 'active');

  // Generate auth tokens
  const superUserToken = generateAuthToken(superUser);
  const adminToken = generateAuthToken(adminUser);

  return {
    group,
    superUser,
    adminUser,
    superUserToken,
    adminToken,
    tempGroup
  };
}

/**
 * Create multiple test users for a group
 */
async function createMultipleUsers(group, count, baseUsername = 'user') {
  const users = [];
  for (let i = 1; i <= count; i++) {
    const user = await createTestUser({
      username: `${baseUsername}${i}`,
      email: `${baseUsername}${i}@test.com`,
      firstName: `User`,
      lastName: `${i}`,
      groupStatus: 'active'
    }, group);
    
    await createGroupMembership(group, user, 'member', 'active');
    users.push(user);
  }
  return users;
}

module.exports = {
  createTestGroup,
  createTestUser,
  createGroupMembership,
  generateAuthToken,
  createTestSetup,
  createMultipleUsers
};
