# API Testing Framework

This directory contains comprehensive API tests for the Requirements Tool group management functionality.

## Test Structure

### Setup
- **Jest** - Testing framework
- **Supertest** - HTTP assertion library
- **MongoDB Atlas** - Test database (separate from production)
- **Test Helpers** - Utilities for creating test data

### Test Files

#### `basic.test.js`
- Basic API connectivity tests
- Route validation
- Health checks

#### `groups.test.js` 
- **Max Seats Enforcement** (3 tests)
  - Enforces seat limits when adding users
  - Prevents reactivation when at capacity
  - Allows reactivation when seats available

- **Group Creation with Duplicate Names** (2 tests)
  - Allows same names with different codes
  - Prevents duplicate group codes

- **Complex User State Transitions** (3 tests)
  - Active → Inactive → Admin → Active beyond max seats
  - Prevents demoting last administrator
  - Allows demoting when multiple admins exist

- **Edge Cases and Error Scenarios** (5 tests)
  - Tier changes and seat limit violations
  - Super user self-removal prevention
  - Concurrent user additions
  - Invalid user ID handling
  - Group code validation

#### `comprehensive.test.js`
- End-to-end workflow tests
- Complex multi-step scenarios
- Concurrent operations testing

## Key Test Scenarios

### 1. Max Seats Enforcement
Tests that groups properly enforce their tier-based user limits:
- Small tier (1-3 users): 3 max users
- Medium tier (4-25 users): 25 max users
- Large tier (26-100 users): 100 max users
- Enterprise tier (100+ users): 500 max users

### 2. User State Transitions
Tests complex user lifecycle scenarios:
```
Active User → Deactivated → Promoted to Admin → Reactivated
```

### 3. Administrator Role Management
- Prevents removing the last administrator
- Allows role changes when multiple admins exist
- Enforces admin-only operations

### 4. Data Integrity
- Handles concurrent operations safely
- Validates input data properly
- Maintains referential integrity

## Running Tests

```bash
# Run all tests
npm test

# Run specific test file
npm test -- server/tests/api/groups.test.js

# Run with verbose output
npm test -- --verbose

# Run with coverage
npm run test:coverage

# Watch mode for development
npm run test:watch
```

## Test Results Summary

✅ **17 Total Tests**
- 13 Group Management Tests
- 2 Basic API Tests  
- 2 Comprehensive Workflow Tests

### Key Validations Covered:
- ✅ Max seat enforcement across all tiers
- ✅ Group creation with duplicate name/code handling
- ✅ Complex user state transitions
- ✅ Administrator role constraints
- ✅ Super user protection
- ✅ Concurrent operation safety
- ✅ Input validation and error handling
- ✅ Data consistency and integrity

## Test Database

Tests use a separate MongoDB Atlas database (`requirements-tool-test`) to avoid affecting production data. The test setup:

1. Connects to test database before all tests
2. Cleans up data after each test
3. Disconnects after all tests complete

## Adding New Tests

1. Create test files in `server/tests/api/`
2. Use test helpers from `server/tests/helpers/testHelpers.js`
3. Follow existing patterns for setup/teardown
4. Include both positive and negative test cases
5. Test edge cases and error conditions
