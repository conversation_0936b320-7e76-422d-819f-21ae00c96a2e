const request = require('supertest');
const app = require('../../app');
const {
  createTestSetup,
  createTestGroup,
  createTestUser,
  createGroupMembership,
  createMultipleUsers,
  generateAuthToken
} = require('../helpers/testHelpers');
const Group = require('../../models/Group');
const User = require('../../models/User');
const GroupMembership = require('../../models/GroupMembership');

describe('Group Management API Tests', () => {
  let testSetup;

  beforeEach(async () => {
    testSetup = await createTestSetup();
  });

  describe('Max Seats Enforcement', () => {
    test('should enforce max seats when adding users to small tier group (1-3)', async () => {
      const { group, adminToken } = testSetup;
      
      // Group should have max 3 users, admin is already 1
      const newUsers = [
        { username: 'user1', email: '<EMAIL>', firstName: 'User', lastName: '1' },
        { username: 'user2', email: '<EMAIL>', firstName: 'User', lastName: '2' },
        { username: 'user3', email: '<EMAIL>', firstName: 'User', lastName: '3' },
        { username: 'user4', email: '<EMAIL>', firstName: 'User', lastName: '4' } // This should fail
      ];

      // Add first 2 users (should succeed - total 3 with admin)
      const response1 = await request(app)
        .post(`/api/groups/${group._id}/members`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ users: newUsers.slice(0, 2) });

      expect(response1.status).toBe(200);
      expect(response1.body.results).toHaveLength(2);
      expect(response1.body.results.every(r => r.success)).toBe(true);

      // Try to add 2 more users (should fail - would exceed max)
      const response2 = await request(app)
        .post(`/api/groups/${group._id}/members`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ users: newUsers.slice(2) });

      expect(response2.status).toBe(200);
      // Should still create pending users (seat limits enforced during activation)
      expect(response2.body.results).toHaveLength(2);
    });

    test('should prevent reactivation when max seats reached', async () => {
      const { group, adminToken } = testSetup;
      
      // Fill up the group to max capacity (3 users including admin)
      const users = await createMultipleUsers(group, 2, 'testuser');
      
      // Deactivate one user
      const userToDeactivate = users[0];
      await request(app)
        .delete(`/api/groups/${group._id}/members/${userToDeactivate._id}`)
        .set('Authorization', `Bearer ${adminToken}`);

      // Add a new user to fill the spot
      const newUser = await createTestUser({
        username: 'newuser',
        email: '<EMAIL>',
        groupStatus: 'active'
      }, group);
      await createGroupMembership(group, newUser, 'member', 'active');

      // Try to reactivate the deactivated user (should fail - no seats available)
      const response = await request(app)
        .post(`/api/groups/${group._id}/members/${userToDeactivate._id}/reactivate`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('maximum number of active members');
    });

    test('should allow reactivation when seats become available', async () => {
      const { group, adminToken } = testSetup;
      
      // Create and then deactivate a user
      const user = await createTestUser({
        username: 'testuser',
        email: '<EMAIL>',
        groupStatus: 'active'
      }, group);
      await createGroupMembership(group, user, 'member', 'active');

      // Deactivate the user
      await request(app)
        .delete(`/api/groups/${group._id}/members/${user._id}`)
        .set('Authorization', `Bearer ${adminToken}`);

      // Reactivate should work (seats available)
      const response = await request(app)
        .post(`/api/groups/${group._id}/members/${user._id}/reactivate`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.message).toContain('reactivated successfully');
    });
  });

  describe('Group Creation with Duplicate Names', () => {
    test('should allow groups with same name but different codes', async () => {
      const { superUser } = testSetup;

      const groupData1 = {
        name: 'Duplicate Name Group',
        code: 'duplicate-1',
        tier: '1-3',
        createdBy: superUser._id
      };

      const groupData2 = {
        name: 'Duplicate Name Group', // Same name
        code: 'duplicate-2',          // Different code
        tier: '1-3',
        createdBy: superUser._id
      };

      // Create first group
      const response1 = await request(app)
        .post('/api/groups')
        .send(groupData1);

      expect(response1.status).toBe(201);

      // Create second group with same name but different code
      const response2 = await request(app)
        .post('/api/groups')
        .send(groupData2);

      expect(response2.status).toBe(201);
      expect(response2.body.name).toBe(groupData2.name);
      expect(response2.body.code).toBe(groupData2.code);
    });

    test('should prevent groups with duplicate codes', async () => {
      const { superUser } = testSetup;

      const groupData1 = {
        name: 'First Group',
        code: 'same-code',
        tier: '1-3',
        createdBy: superUser._id
      };

      const groupData2 = {
        name: 'Second Group',
        code: 'same-code', // Same code
        tier: '1-3',
        createdBy: superUser._id
      };

      // Create first group
      const response1 = await request(app)
        .post('/api/groups')
        .send(groupData1);

      expect(response1.status).toBe(201);

      // Try to create second group with same code (should fail)
      const response2 = await request(app)
        .post('/api/groups')
        .send(groupData2);

      expect(response2.status).toBe(400);
      expect(response2.body.message).toContain('already exists');
    });
  });

  describe('Complex User State Transitions', () => {
    test('should handle active → inactive → admin → active beyond max seats scenario', async () => {
      const { group, adminToken } = testSetup;

      // Step 1: Create a user and make them active
      const user = await createTestUser({
        username: 'transitionuser',
        email: '<EMAIL>',
        groupStatus: 'active'
      }, group);
      await createGroupMembership(group, user, 'member', 'active');

      // Verify user is active
      let membership = await GroupMembership.findOne({ group: group._id, user: user._id });
      expect(membership.status).toBe('active');
      expect(membership.role).toBe('member');

      // Step 2: Deactivate the user
      const deactivateResponse = await request(app)
        .delete(`/api/groups/${group._id}/members/${user._id}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(deactivateResponse.status).toBe(200);

      // Verify user is inactive
      membership = await GroupMembership.findOne({ group: group._id, user: user._id });
      expect(membership.status).toBe('inactive');

      // Step 3: Fill up the group to max capacity with other users
      const fillerUsers = await createMultipleUsers(group, 2, 'filler');

      // Verify group is at capacity (admin + 2 filler users = 3/3)
      const activeCount = await GroupMembership.countDocuments({
        group: group._id,
        status: 'active'
      });
      expect(activeCount).toBe(3);

      // Step 4: Try to reactivate user (should fail - no seats)
      const reactivateResponse1 = await request(app)
        .post(`/api/groups/${group._id}/members/${user._id}/reactivate`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(reactivateResponse1.status).toBe(400);
      expect(reactivateResponse1.body.message).toContain('maximum number of active members');

      // Step 5: Promote the inactive user to admin (should work even when no seats)
      const promoteResponse = await request(app)
        .put(`/api/groups/${group._id}/members/${user._id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ role: 'administrator' });

      expect(promoteResponse.status).toBe(200);

      // Verify role changed but still inactive
      membership = await GroupMembership.findOne({ group: group._id, user: user._id });
      expect(membership.role).toBe('administrator');
      expect(membership.status).toBe('inactive');

      // Step 6: Now reactivate the admin user
      // NOTE: Administrators can be reactivated beyond seat limits to ensure group management continuity
      // This allows the admin to always access and manage the group, even when at capacity
      const reactivateResponse2 = await request(app)
        .post(`/api/groups/${group._id}/members/${user._id}/reactivate`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(reactivateResponse2.status).toBe(200);

      // Verify user is now active admin
      membership = await GroupMembership.findOne({ group: group._id, user: user._id });
      expect(membership.status).toBe('active');
      expect(membership.role).toBe('administrator');

      // Verify group now has 4 active users (admin reactivation exceeded the 3-user limit)
      const finalActiveCount = await GroupMembership.countDocuments({
        group: group._id,
        status: 'active'
      });
      expect(finalActiveCount).toBe(4); // Exceeded limit due to admin reactivation privilege
    });

    test('should prevent demoting the last administrator', async () => {
      const { group, adminUser, adminToken } = testSetup;

      // Try to demote the only admin to member (should fail)
      const response = await request(app)
        .put(`/api/groups/${group._id}/members/${adminUser._id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ role: 'member' });

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('Cannot demote the last administrator');
    });

    test('should allow demoting admin when multiple admins exist', async () => {
      const { group, adminUser, adminToken } = testSetup;

      // Create another admin
      const secondAdmin = await createTestUser({
        username: 'admin2',
        email: '<EMAIL>',
        groupStatus: 'active'
      }, group);
      await createGroupMembership(group, secondAdmin, 'administrator', 'active');

      // Now demote the first admin (should work)
      const response = await request(app)
        .put(`/api/groups/${group._id}/members/${adminUser._id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ role: 'member' });

      expect(response.status).toBe(200);

      // Verify demotion
      const membership = await GroupMembership.findOne({
        group: group._id,
        user: adminUser._id
      });
      expect(membership.role).toBe('member');
    });
  });

  describe('Edge Cases and Error Scenarios', () => {
    test('should handle tier changes and seat limit violations', async () => {
      const { group, adminToken } = testSetup;

      // Fill group to capacity (3 users)
      await createMultipleUsers(group, 2, 'user');

      // Try to downgrade tier to smaller capacity
      const response = await request(app)
        .put(`/api/groups/${group._id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ tier: '1-3' }); // Already at this tier, but test the logic

      expect(response.status).toBe(200);

      // Verify max users is still correct
      const updatedGroup = await Group.findById(group._id);
      expect(updatedGroup.maxUsers).toBe(3);
    });

    test('should prevent super user from removing themselves', async () => {
      const { superUser, superUserToken, group } = testSetup;

      // Create membership for super user in the test group
      await createGroupMembership(group, superUser, 'administrator', 'active');

      // Try to remove super user (should fail)
      const response = await request(app)
        .delete(`/api/groups/${group._id}/members/${superUser._id}`)
        .set('Authorization', `Bearer ${superUserToken}`);

      expect(response.status).toBe(403);
      expect(response.body.message).toContain('Super users cannot remove themselves');
    });

    test('should handle concurrent user additions near seat limit', async () => {
      const { group, adminToken } = testSetup;

      // Add users to bring close to limit (admin + 1 = 2/3)
      await createMultipleUsers(group, 1, 'existing');

      // Simulate concurrent requests to add users
      const newUsers1 = [
        { username: 'concurrent1', email: '<EMAIL>', firstName: 'User', lastName: '1' }
      ];
      const newUsers2 = [
        { username: 'concurrent2', email: '<EMAIL>', firstName: 'User', lastName: '2' }
      ];

      // Both requests should succeed (pending users can exceed limits)
      const [response1, response2] = await Promise.all([
        request(app)
          .post(`/api/groups/${group._id}/members`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send({ users: newUsers1 }),
        request(app)
          .post(`/api/groups/${group._id}/members`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send({ users: newUsers2 })
      ]);

      expect(response1.status).toBe(200);
      expect(response2.status).toBe(200);
      expect(response1.body.results[0].success).toBe(true);
      expect(response2.body.results[0].success).toBe(true);
    });

    test('should handle invalid user IDs gracefully', async () => {
      const { group, adminToken } = testSetup;
      const invalidUserId = '507f1f77bcf86cd799439011'; // Valid ObjectId format but non-existent

      // Try to remove non-existent user
      const removeResponse = await request(app)
        .delete(`/api/groups/${group._id}/members/${invalidUserId}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(removeResponse.status).toBe(404);
      expect(removeResponse.body.message).toContain('User not found');

      // Try to reactivate non-existent user
      const reactivateResponse = await request(app)
        .post(`/api/groups/${group._id}/members/${invalidUserId}/reactivate`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(reactivateResponse.status).toBe(404);
      expect(reactivateResponse.body.message).toContain('User not found in group');

      // Try to update role of non-existent user
      const roleResponse = await request(app)
        .put(`/api/groups/${group._id}/members/${invalidUserId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ role: 'administrator' });

      expect(roleResponse.status).toBe(404);
      expect(roleResponse.body.message).toContain('User not found in group');
    });

    test('should validate group code format and availability', async () => {
      const { superUser } = testSetup;

      // Test valid group code first
      const validResponse = await request(app)
        .post('/api/groups')
        .send({
          name: 'Valid Group',
          code: 'valid-group-code',
          tier: '1-3',
          createdBy: superUser._id
        });

      expect(validResponse.status).toBe(201);

      // Test duplicate code (should fail)
      const duplicateResponse = await request(app)
        .post('/api/groups')
        .send({
          name: 'Another Group',
          code: 'valid-group-code', // Same code as above
          tier: '1-3',
          createdBy: superUser._id
        });

      expect(duplicateResponse.status).toBe(400);
      expect(duplicateResponse.body.message).toContain('already exists');
    });
  });
});
