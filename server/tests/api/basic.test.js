const request = require('supertest');
const app = require('../../app');

describe('Basic API Tests', () => {
  test('should respond to health check', async () => {
    // Test a simple endpoint to verify the app is working
    const response = await request(app)
      .post('/api/groups/validate-code')
      .send({ groupCode: 'non-existent' });

    // Should get a 400 for non-existent group, but app should be running
    expect(response.status).toBe(400);
    expect(response.body.message).toContain('Group not found');
  });

  test('should handle invalid routes', async () => {
    const response = await request(app)
      .get('/api/invalid-route');

    expect(response.status).toBe(404);
  });
});
