const request = require('supertest');
const app = require('../../app');
const {
  createTestSetup,
  createTestUser,
  createTestGroup,
  generateAuthToken
} = require('../helpers/testHelpers');
const Requirement = require('../../models/Requirement');
const Feature = require('../../models/Feature');
const Project = require('../../models/Project');

describe('Requirements API Tests', () => {
  let testSetup;
  let testProject;
  let testFeature;

  beforeEach(async () => {
    testSetup = await createTestSetup();
    
    // Create a test project
    testProject = new Project({
      name: 'Test Project',
      description: 'A test project for requirements',
      group: testSetup.group._id,
      createdBy: testSetup.adminUser._id
    });
    await testProject.save();

    // Create a test feature
    testFeature = new Feature({
      project: testProject._id,
      group: testSetup.group._id,
      versions: [{
        version: 1,
        title: 'Test Feature',
        description: 'A test feature for requirements',
        createdBy: testSetup.adminUser._id
      }],
      createdBy: testSetup.adminUser._id
    });
    await testFeature.save();
  });

  describe('Requirement Creation', () => {
    test('should create a requirement with proper group context', async () => {
      const { adminToken } = testSetup;
      
      const requirementData = {
        project: testProject._id,
        feature: testFeature._id,
        title: 'Test Requirement',
        description: 'A test requirement for multi-tenancy',
        type: 'requirement'
      };

      const response = await request(app)
        .post('/api/requirements')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(requirementData);

      expect(response.status).toBe(200);
      expect(response.body.group).toBe(testSetup.group._id.toString());
      expect(response.body.versions[0].title).toBe(requirementData.title);
      expect(response.body.versions[0].description).toBe(requirementData.description);
      expect(response.body.type).toBe(requirementData.type);
    });

    test('should create a user story with proper group context', async () => {
      const { adminToken } = testSetup;
      
      const userStoryData = {
        project: testProject._id,
        feature: testFeature._id,
        title: 'Test User Story',
        description: 'As a user, I want to test multi-tenancy',
        type: 'user_story'
      };

      const response = await request(app)
        .post('/api/requirements')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(userStoryData);

      expect(response.status).toBe(200);
      expect(response.body.group).toBe(testSetup.group._id.toString());
      expect(response.body.type).toBe('user_story');
    });

    test('should prevent inactive users from creating requirements', async () => {
      const { group } = testSetup;
      
      // Create an inactive user
      const inactiveUser = await createTestUser({
        username: 'inactive',
        email: '<EMAIL>',
        groupStatus: 'inactive'
      }, group);
      
      const inactiveToken = generateAuthToken(inactiveUser);
      
      const requirementData = {
        project: testProject._id,
        feature: testFeature._id,
        title: 'Test Requirement',
        description: 'Should not be created',
        type: 'requirement'
      };

      const response = await request(app)
        .post('/api/requirements')
        .set('Authorization', `Bearer ${inactiveToken}`)
        .send(requirementData);

      expect(response.status).toBe(403);
      expect(response.body.message).toContain('inactive');
    });
  });

  describe('Requirement Retrieval with Multi-tenancy', () => {
    test('should only return requirements from user\'s group', async () => {
      const { adminToken, group } = testSetup;
      
      // Create a requirement in the test group
      const requirement1 = new Requirement({
        project: testProject._id,
        feature: testFeature._id,
        group: group._id,
        type: 'requirement',
        versions: [{
          version: 1,
          title: 'Group Requirement',
          description: 'Requirement in test group',
          createdBy: testSetup.adminUser._id
        }],
        createdBy: testSetup.adminUser._id
      });
      await requirement1.save();

      // Create another group and requirement
      const otherGroup = await createTestGroup('Other Group', 'other-group', '1-3', testSetup.superUser);
      const otherUser = await createTestUser({
        username: 'otheruser',
        email: '<EMAIL>',
        groupStatus: 'active'
      }, otherGroup);
      
      const otherProject = new Project({
        name: 'Other Project',
        description: 'Project in other group',
        group: otherGroup._id,
        createdBy: otherUser._id
      });
      await otherProject.save();

      const otherFeature = new Feature({
        project: otherProject._id,
        group: otherGroup._id,
        versions: [{
          version: 1,
          title: 'Other Feature',
          description: 'Feature in other group',
          createdBy: otherUser._id
        }],
        createdBy: otherUser._id
      });
      await otherFeature.save();

      const requirement2 = new Requirement({
        project: otherProject._id,
        feature: otherFeature._id,
        group: otherGroup._id,
        type: 'requirement',
        versions: [{
          version: 1,
          title: 'Other Group Requirement',
          description: 'Requirement in other group',
          createdBy: otherUser._id
        }],
        createdBy: otherUser._id
      });
      await requirement2.save();

      // Request requirements as test group user
      const response = await request(app)
        .get('/api/requirements')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.requirements).toHaveLength(1);
      expect(response.body.requirements[0].group).toBe(group._id.toString());
      expect(response.body.requirements[0].versions[0].title).toBe('Group Requirement');
    });

    test('should allow super users to see all requirements', async () => {
      const { superUserToken, group } = testSetup;
      
      // Create requirements in different groups
      const requirement1 = new Requirement({
        project: testProject._id,
        feature: testFeature._id,
        group: group._id,
        type: 'requirement',
        versions: [{
          version: 1,
          title: 'Group 1 Requirement',
          description: 'Requirement in group 1',
          createdBy: testSetup.adminUser._id
        }],
        createdBy: testSetup.adminUser._id
      });
      await requirement1.save();

      const otherGroup = await createTestGroup('Other Group', 'other-group', '1-3', testSetup.superUser);
      const otherUser = await createTestUser({
        username: 'otheruser',
        email: '<EMAIL>',
        groupStatus: 'active'
      }, otherGroup);
      
      const otherProject = new Project({
        name: 'Other Project',
        description: 'Project in other group',
        group: otherGroup._id,
        createdBy: otherUser._id
      });
      await otherProject.save();

      const otherFeature = new Feature({
        project: otherProject._id,
        group: otherGroup._id,
        versions: [{
          version: 1,
          title: 'Other Feature',
          description: 'Feature in other group',
          createdBy: otherUser._id
        }],
        createdBy: otherUser._id
      });
      await otherFeature.save();

      const requirement2 = new Requirement({
        project: otherProject._id,
        feature: otherFeature._id,
        group: otherGroup._id,
        type: 'requirement',
        versions: [{
          version: 1,
          title: 'Group 2 Requirement',
          description: 'Requirement in group 2',
          createdBy: otherUser._id
        }],
        createdBy: otherUser._id
      });
      await requirement2.save();

      // Request requirements as super user
      const response = await request(app)
        .get('/api/requirements')
        .set('Authorization', `Bearer ${superUserToken}`);

      expect(response.status).toBe(200);
      expect(response.body.requirements).toHaveLength(2);
      
      const titles = response.body.requirements.map(r => r.versions[0].title);
      expect(titles).toContain('Group 1 Requirement');
      expect(titles).toContain('Group 2 Requirement');
    });

    test('should handle navigation requests with group filtering', async () => {
      const { adminToken, group } = testSetup;
      
      // Create a requirement in the test group
      const requirement = new Requirement({
        project: testProject._id,
        feature: testFeature._id,
        group: group._id,
        type: 'requirement',
        versions: [{
          version: 1,
          title: 'Navigation Test Requirement',
          description: 'For navigation testing',
          createdBy: testSetup.adminUser._id
        }],
        createdBy: testSetup.adminUser._id
      });
      await requirement.save();

      // Request requirements for navigation
      const response = await request(app)
        .get('/api/requirements?navigation=true')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body).toHaveLength(1);
      expect(response.body[0].group).toBe(group._id.toString());
    });
  });

  describe('Requirement Data Integrity', () => {
    test('should maintain group consistency when creating requirements', async () => {
      const { adminToken } = testSetup;
      
      const requirementData = {
        project: testProject._id,
        feature: testFeature._id,
        title: 'Consistency Test Requirement',
        description: 'Testing group consistency',
        type: 'requirement'
      };

      const response = await request(app)
        .post('/api/requirements')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(requirementData);

      expect(response.status).toBe(200);
      
      // Verify in database
      const savedRequirement = await Requirement.findById(response.body._id);
      expect(savedRequirement.group.toString()).toBe(testSetup.group._id.toString());
      expect(savedRequirement.project.toString()).toBe(testProject._id.toString());
      expect(savedRequirement.feature.toString()).toBe(testFeature._id.toString());
    });
  });
});
