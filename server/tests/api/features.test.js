const request = require('supertest');
const app = require('../../app');
const {
  createTestSetup,
  createTestUser,
  createTestGroup,
  generateAuthToken
} = require('../helpers/testHelpers');
const Feature = require('../../models/Feature');
const Project = require('../../models/Project');

describe('Features API Tests', () => {
  let testSetup;
  let testProject;

  beforeEach(async () => {
    testSetup = await createTestSetup();
    
    // Create a test project for features
    testProject = new Project({
      name: 'Test Project',
      description: 'A test project for features',
      group: testSetup.group._id,
      createdBy: testSetup.adminUser._id
    });
    await testProject.save();
  });

  describe('Feature Creation', () => {
    test('should create a feature with proper group context', async () => {
      const { adminToken } = testSetup;
      
      const featureData = {
        project: testProject._id,
        title: 'Test Feature',
        description: 'A test feature for multi-tenancy'
      };

      const response = await request(app)
        .post('/api/features')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(featureData);

      expect(response.status).toBe(200);
      expect(response.body.group).toBe(testSetup.group._id.toString());
      expect(response.body.versions[0].title).toBe(featureData.title);
      expect(response.body.versions[0].description).toBe(featureData.description);
    });

    test('should prevent inactive users from creating features', async () => {
      const { group } = testSetup;
      
      // Create an inactive user
      const inactiveUser = await createTestUser({
        username: 'inactive',
        email: '<EMAIL>',
        groupStatus: 'inactive'
      }, group);
      
      const inactiveToken = generateAuthToken(inactiveUser);
      
      const featureData = {
        project: testProject._id,
        title: 'Test Feature',
        description: 'Should not be created'
      };

      const response = await request(app)
        .post('/api/features')
        .set('Authorization', `Bearer ${inactiveToken}`)
        .send(featureData);

      expect(response.status).toBe(403);
      expect(response.body.message).toContain('inactive');
    });
  });

  describe('Feature Retrieval with Multi-tenancy', () => {
    test('should only return features from user\'s group', async () => {
      const { adminToken, group } = testSetup;
      
      // Create a feature in the test group
      const feature1 = new Feature({
        project: testProject._id,
        group: group._id,
        versions: [{
          version: 1,
          title: 'Group Feature',
          description: 'Feature in test group',
          createdBy: testSetup.adminUser._id
        }],
        createdBy: testSetup.adminUser._id
      });
      await feature1.save();

      // Create another group and feature
      const otherGroup = await createTestGroup('Other Group', 'other-group', '1-3', testSetup.superUser);
      const otherUser = await createTestUser({
        username: 'otheruser',
        email: '<EMAIL>',
        groupStatus: 'active'
      }, otherGroup);
      
      const otherProject = new Project({
        name: 'Other Project',
        description: 'Project in other group',
        group: otherGroup._id,
        createdBy: otherUser._id
      });
      await otherProject.save();

      const feature2 = new Feature({
        project: otherProject._id,
        group: otherGroup._id,
        versions: [{
          version: 1,
          title: 'Other Group Feature',
          description: 'Feature in other group',
          createdBy: otherUser._id
        }],
        createdBy: otherUser._id
      });
      await feature2.save();

      // Request features as test group user
      const response = await request(app)
        .get('/api/features')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveLength(1);
      expect(response.body[0].group).toBe(group._id.toString());
      expect(response.body[0].versions[0].title).toBe('Group Feature');
    });

    test('should allow super users to see all features', async () => {
      const { superUserToken, group } = testSetup;
      
      // Create features in different groups
      const feature1 = new Feature({
        project: testProject._id,
        group: group._id,
        versions: [{
          version: 1,
          title: 'Group 1 Feature',
          description: 'Feature in group 1',
          createdBy: testSetup.adminUser._id
        }],
        createdBy: testSetup.adminUser._id
      });
      await feature1.save();

      const otherGroup = await createTestGroup('Other Group', 'other-group', '1-3', testSetup.superUser);
      const otherUser = await createTestUser({
        username: 'otheruser',
        email: '<EMAIL>',
        groupStatus: 'active'
      }, otherGroup);
      
      const otherProject = new Project({
        name: 'Other Project',
        description: 'Project in other group',
        group: otherGroup._id,
        createdBy: otherUser._id
      });
      await otherProject.save();

      const feature2 = new Feature({
        project: otherProject._id,
        group: otherGroup._id,
        versions: [{
          version: 1,
          title: 'Group 2 Feature',
          description: 'Feature in group 2',
          createdBy: otherUser._id
        }],
        createdBy: otherUser._id
      });
      await feature2.save();

      // Request features as super user
      const response = await request(app)
        .get('/api/features')
        .set('Authorization', `Bearer ${superUserToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveLength(2);
      
      const titles = response.body.map(f => f.versions[0].title);
      expect(titles).toContain('Group 1 Feature');
      expect(titles).toContain('Group 2 Feature');
    });
  });

  describe('Feature Data Integrity', () => {
    test('should maintain group consistency when creating features', async () => {
      const { adminToken } = testSetup;
      
      const featureData = {
        project: testProject._id,
        title: 'Consistency Test Feature',
        description: 'Testing group consistency'
      };

      const response = await request(app)
        .post('/api/features')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(featureData);

      expect(response.status).toBe(200);
      
      // Verify in database
      const savedFeature = await Feature.findById(response.body._id);
      expect(savedFeature.group.toString()).toBe(testSetup.group._id.toString());
      expect(savedFeature.project.toString()).toBe(testProject._id.toString());
    });
  });
});
