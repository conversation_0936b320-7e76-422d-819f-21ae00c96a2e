{"name": "requirements-tool", "version": "1.0.0", "description": "A web-based requirements management tool for software development teams", "main": "server/server.js", "scripts": {"start": "node server/server.js", "dev": "nodemon server/server.js", "client": "cd client && npm start", "dev:full": "concurrently \"npm run dev\" \"npm run client\"", "create-indexes": "node server/scripts/createIndexes.js", "cleanup-large-docs": "node server/scripts/cleanupLargeDocuments.js", "test": "NODE_ENV=test jest --detectOpenHandles --forceExit", "test:watch": "NODE_ENV=test jest --watch --detectOpenHandles", "test:coverage": "NODE_ENV=test jest --coverage --detectOpenHandles --forceExit"}, "dependencies": {"axios": "^1.9.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "mongodb": "^6.16.0", "mongoose": "^7.6.3", "multer": "^2.0.1", "quill": "^2.0.3", "react-beautiful-dnd": "^13.1.1", "react-quill": "^2.0.0", "socket.io": "^4.7.2"}, "devDependencies": {"@types/jest": "^29.5.8", "concurrently": "^8.2.2", "jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^6.3.3"}}