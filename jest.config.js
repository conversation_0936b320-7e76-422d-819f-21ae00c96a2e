module.exports = {
  testEnvironment: 'node',
  testMatch: ['**/__tests__/**/*.js', '**/?(*.)+(spec|test).js'],
  testPathIgnorePatterns: ['/node_modules/', '/client/'],
  setupFilesAfterEnv: ['./server/tests/setup.js'],
  verbose: true,
  forceExit: true,
  clearMocks: true,
  resetMocks: true,
  restoreMocks: true,
  testTimeout: 30000,
  collectCoverageFrom: [
    'server/**/*.js',
    '!server/tests/**',
    '!server/scripts/**',
    '!server/server.js',
    '!server/app.js'
  ]
};
