---
type: "always_apply"
---

# Code Guidelines  
## Keyboard Shortcuts

| Trigger | What it should do |
|---------|------------------|
| **qcheck** | Act as a skeptical senior software engineer, review every **major** code change (skip minor) against &nbsp;1) Writing Functions Best Practices, 2) Writing Tests Best Practices, 3) Implementation Best Practices. |
| **qcheckf** | Same reviewer mindset, but focus only on every **major function** you added or edited, using the Writing Functions Best Practices checklist. |
| **qcheckt** | Same reviewer mindset, but focus only on every **major test** you added or edited, using the Writing Tests Best Practices checklist. |
| **qux** | Imagine you are a human UX tester of the implemented feature, list the highest-priority scenarios you would test. |
| **qgit** | Stage all changes, create a commit, push to remote. Use the Conventional Commits format for the message. |
| **qnew** | Ensure the code *always* follows every best practice listed below. |
| **qplan** | Analyse similar parts of the codebase and confirm your plan: ① fits the existing style, ② keeps changes small, ③ reuses existing code. |
| **qcode** | Implement the plan, run the new tests, make sure nothing else breaks, and apply formatting & type-checking. |

---

## Implementation Best Practices

### 0, Purpose  
Keep the code maintainable, safe, and quick to evolve. **MUST** rules are enforced by CI, **SHOULD** rules are strongly advised.

### 1, Before Coding  
1. **BP-1 (MUST)** Ask the user clarifying questions.  
2. **BP-2 (SHOULD)** Draft and confirm an approach for complex tasks.  
3. **BP-3 (SHOULD)** If several approaches exist, list clear pros and cons.

### 2, While Coding  
1. **C-1 (MUST)** Follow TDD: stub → write failing test → implement.  
2. **C-2 (MUST)** Name functions with the domain vocabulary already used.  
3. **C-3 (SHOULD NOT)** Introduce classes when small testable functions are enough.  
4. **C-4 (SHOULD)** Prefer simple, composable, and testable functions.  
5. **C-5 (MUST)** Use branded `type`s for IDs.  
6. **C-6 (MUST)** Import types with `import type { … }`.  
7. **C-7 (SHOULD NOT)** Add comments unless they explain a real caveat; favour self-explanatory code.  
8. **C-8 (SHOULD)** Default to `type`; use `interface` only when merging or readability demands it.  
9. **C-9 (SHOULD NOT)** Extract a new function unless it will be reused, is required for unit-testing, or greatly clarifies opaque code.

### 3, Testing  
1. **T-1 (MUST)** For a simple function, keep its unit tests beside the source file (`*.spec.ts`).  
2. **T-2 (MUST)** For any API change, add or extend integration tests in `packages/api/test/*.spec.ts`.  
3. **T-3 (MUST)** Separate pure-logic unit tests from DB-touching integration tests.  
4. **T-4 (SHOULD)** Favour integration tests over heavy mocking.  
5. **T-5 (SHOULD)** Unit-test complex algorithms in depth.  
6. **T-6 (SHOULD)** Assert the whole structure in one check when feasible.

### 4, Database  
1. **D-1 (MUST)** Type DB helpers as `KyselyDatabase | Transaction<Database>` so they work with and without a transaction.  
2. **D-2 (SHOULD)** Override incorrect generated types in `packages/shared/src/db-types.override.ts` (e.g. wrong `BigInt`).

### 5, Code Organization  
1. **O-1 (MUST)** Put code in `packages/shared` only if it is used by **≥ 2** packages.

### 6, Tooling Gates  
1. **G-1 (MUST)** `prettier --check` must pass.  
2. **G-2 (MUST)** `turbo typecheck lint` must pass.

### 7, Git  
1. **GH-1 (MUST)** Use the Conventional Commits format.  
2. **GH-2 (SHOULD NOT)** Mention Claude or Anthropic in commit messages.

---

## Writing Functions Best Practices

When you judge a function you wrote, ask:  

1. Can you read it and *honestly* follow the logic quickly?  
2. Is cyclomatic complexity high? If yes, rethink.  
3. Would a known data structure or algorithm make it clearer (parser, tree, queue, etc.)?  
4. Does it have unused parameters?  
5. Could any type cast be replaced by a parameter?  
6. Is it easily testable without mocking core services (SQL, Redis)? If not, can it be covered by an integration test?  
7. Are there hidden dependencies that should be passed in as arguments? Focus on ones that can change behaviour.  
8. Brainstorm up to three clearer names and choose the one that fits the codebase.

Only split out a new helper function when  
• it will be reused elsewhere,  
• it enables unit-testing otherwise untestable logic, **or**  
• it radically improves readability of difficult code.

---

## Writing Tests Best Practices

1. Parameterise inputs; avoid magic literals like `42` or `"foo"`.  
2. Add a test only if it can fail for a genuine defect; trivial asserts are forbidden.  
3. The test description must state exactly what is verified; if words and assert drift, rename or rewrite.  
4. Compare against pre-computed expectations rooted in the domain, not the function’s own output reused as the oracle.  
5. Apply the same lint, type-safety, and style rules as production code.  
6. Prefer property-based tests to single hard-coded cases when practical (e.g. `fast-check`).  
7. Group unit tests under `describe(functionName, () => …)` and use `expect.any(…)` for flexible arguments.  
8. Use strong assertions like `expect(x).toEqual(1)` rather than weaker ones like `expect(x).toBeGreaterThanOrEqual(1)`.  
9. Cover edge cases, realistic input, unexpected input, and boundary values.  
10. Skip conditions already enforced by the type checker.

---

## Folder Overview