{"name": "requirements-tool-client", "version": "0.1.0", "private": true, "proxy": "http://localhost:5000", "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@lexical/code": "^0.32.0", "@lexical/html": "^0.32.0", "@lexical/link": "^0.32.0", "@lexical/list": "^0.32.0", "@lexical/react": "^0.32.0", "@lexical/rich-text": "^0.32.0", "@lexical/selection": "^0.32.0", "@lexical/table": "^0.32.0", "@lexical/utils": "^0.32.0", "@mui/icons-material": "^5.14.3", "@mui/material": "^5.14.3", "@mui/x-data-grid": "^6.11.0", "axios": "^1.5.0", "html2pdf.js": "^0.10.3", "lexical": "^0.32.0", "quill": "^2.0.3", "react": "^18.2.0", "react-datepicker": "^8.4.0", "react-dom": "^18.2.0", "react-grid-layout": "^1.5.2", "react-image-crop": "^11.0.10", "react-quill": "^2.0.0", "react-router-dom": "^6.15.0", "react-scripts": "^5.0.1", "socket.io-client": "^4.7.2"}, "scripts": {"start": "export NODE_OPTIONS=--openssl-legacy-provider && react-scripts start", "build": "export NODE_OPTIONS=--openssl-legacy-provider && react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1"}}