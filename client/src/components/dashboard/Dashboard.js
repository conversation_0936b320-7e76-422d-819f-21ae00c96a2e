import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControlLabel,
  Checkbox
} from '@mui/material';
import { Add as AddIcon } from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';

const Dashboard = () => {
  const navigate = useNavigate();
  const { axios, user } = useAuth();
  const [newProjectDialogOpen, setNewProjectDialogOpen] = useState(false);
  const [newProject, setNewProject] = useState({
    name: '',
    description: '',
    addSelf: true
  });

  const handleCreateProject = async () => {
    try {
      console.log('Dashboard: Creating project with data:', newProject);
      const response = await axios.post('/api/projects', {
        name: newProject.name,
        description: newProject.description,
        addSelf: newProject.addSelf
      });

      console.log('Dashboard: Project creation response:', response.data);

      setNewProjectDialogOpen(false);
      setNewProject({ name: '', description: '', addSelf: true });

      // Trigger navigation update when new project is created
      console.log('Dashboard: Dispatching projectCreated event with user:', {
        userId: user.id || user._id,
        userObject: user,
        projectId: response.data._id
      });
      window.dispatchEvent(new CustomEvent('updateNavigation', {
        detail: {
          type: 'projectCreated',
          projectId: response.data._id,
          userId: user.id || user._id // Current user who created the project
        }
      }));

      // Navigate to the new project
      navigate(`/project/${response.data._id}`);
    } catch (error) {
      console.error('Error creating project:', error);
    }
  };

  return (
    <Box sx={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      height: 'calc(100vh - 56px)', // Account for smaller app bar
      p: 3
    }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <img
          src="/LogoSunday3.svg"
          alt="Requirements Tool Logo"
          style={{
            height: '48px',
            width: 'auto',
            marginRight: '16px'
          }}
        />
        <Typography variant="h4" color="text.secondary">
          Welcome to Requirements Tool
        </Typography>
      </Box>

      <Typography variant="body1" gutterBottom color="text.secondary" sx={{ mb: 4 }}>
        Select a project from the navigation sidebar or create a new one to get started.
      </Typography>

      <Button
        variant="contained"
        size="large"
        startIcon={<AddIcon />}
        onClick={() => setNewProjectDialogOpen(true)}
        sx={{ px: 4, py: 2 }}
      >
        New Project
      </Button>

      {/* Create New Project Dialog */}
      <Dialog
        open={newProjectDialogOpen}
        onClose={() => setNewProjectDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Create New Project</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Project Name"
            fullWidth
            variant="outlined"
            value={newProject.name}
            onChange={(e) => setNewProject({ ...newProject, name: e.target.value })}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="Description"
            fullWidth
            multiline
            rows={3}
            variant="outlined"
            value={newProject.description}
            onChange={(e) => setNewProject({ ...newProject, description: e.target.value })}
            sx={{ mb: 2 }}
          />
          <FormControlLabel
            control={
              <Checkbox
                checked={newProject.addSelf}
                onChange={(e) => setNewProject({ ...newProject, addSelf: e.target.checked })}
              />
            }
            label="Add self to Project"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setNewProjectDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleCreateProject}
            variant="contained"
            disabled={!newProject.name || !newProject.description}
          >
            Create
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Dashboard;
