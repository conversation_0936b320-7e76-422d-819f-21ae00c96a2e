import React, { useState } from 'react';
import {
  Box,
  Chip,
  TextField,
  Button,
  Typography,
  IconButton,
  Tooltip,
  Stack,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Collapse,
  Paper
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  LocalOffer as TagIcon,
  ExpandMore as ExpandMoreIcon,
  ChevronRight as ChevronRightIcon
} from '@mui/icons-material';
import axios from 'axios';

const TagManager = ({
  item,
  itemType, // 'feature' or 'requirement'
  onUpdate,
  bulkTagButton = null, // Optional bulk tag button for features
  sx = {}
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [newTag, setNewTag] = useState('');
  const [isAdding, setIsAdding] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [tagToDelete, setTagToDelete] = useState(null);

  // Get tags from current version
  const currentVersion = item?.versions?.[item.versions.length - 1];
  const releaseTags = currentVersion?.releaseTags || [];

  const handleAddTag = async () => {
    if (!newTag.trim()) return;

    try {
      setLoading(true);
      setError('');
      
      const endpoint = itemType === 'feature' 
        ? `/api/features/${item._id}/tags`
        : `/api/requirements/${item._id}/tags`;
      
      const response = await axios.post(endpoint, {
        tag: newTag.trim()
      });

      if (onUpdate) {
        onUpdate(response.data);
      }

      setNewTag('');
      setIsAdding(false);
    } catch (err) {
      console.error('Error adding tag:', err);
      setError(err.response?.data?.message || 'Failed to add tag');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteTag = async (tag) => {
    try {
      setLoading(true);
      setError('');
      
      const endpoint = itemType === 'feature' 
        ? `/api/features/${item._id}/tags/${encodeURIComponent(tag)}`
        : `/api/requirements/${item._id}/tags/${encodeURIComponent(tag)}`;
      
      const response = await axios.delete(endpoint);

      if (onUpdate) {
        onUpdate(response.data);
      }

      setDeleteDialogOpen(false);
      setTagToDelete(null);
    } catch (err) {
      console.error('Error removing tag:', err);
      setError(err.response?.data?.message || 'Failed to remove tag');
    } finally {
      setLoading(false);
    }
  };

  const openDeleteDialog = (tag) => {
    setTagToDelete(tag);
    setDeleteDialogOpen(true);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Calculate summary for collapsed state
  const totalTags = releaseTags.length;

  return (
    <>
      <Paper sx={{
        p: 2,
        border: '1px solid',
        borderColor: 'divider',
        boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
        ...sx
      }}>
      {/* Collapsible Header */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: isExpanded ? 2 : 0,
          cursor: 'pointer',
          py: 1,
          '&:hover': {
            bgcolor: 'action.hover',
            borderRadius: 1
          }
        }}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <IconButton size="small" sx={{ p: 0 }}>
            {isExpanded ? <ExpandMoreIcon /> : <ChevronRightIcon />}
          </IconButton>
          <TagIcon sx={{ color: 'primary.main' }} />
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Release Management
          </Typography>
          {!isExpanded && (
            <Box sx={{ display: 'flex', gap: 1, ml: 2 }}>
              <Chip
                label={`${totalTags} tag${totalTags !== 1 ? 's' : ''}`}
                size="small"
                variant="outlined"
                color="primary"
              />
            </Box>
          )}
        </Box>
        {isExpanded && (
          <Box sx={{ display: 'flex', gap: 1 }}>
            {bulkTagButton && (
              <Box onClick={(e) => e.stopPropagation()}>
                {bulkTagButton}
              </Box>
            )}
            {!isAdding && (
              <Chip
                label="Add Tag"
                icon={<AddIcon />}
                onClick={(e) => {
                  e.stopPropagation();
                  setIsAdding(true);
                }}
                variant="outlined"
                color="primary"
                size="small"
                sx={{
                  '&:hover': {
                    borderColor: 'primary.main',
                    backgroundColor: 'rgba(25, 118, 210, 0.1)',
                    boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)'
                  }
                }}
              />
            )}
          </Box>
        )}
      </Box>

      {/* Collapsible Content */}
      <Collapse in={isExpanded} timeout="auto" unmountOnExit>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
            {error}
          </Alert>
        )}

        {/* Existing Tags */}
        <Stack direction="row" spacing={1} sx={{ mb: 2, flexWrap: 'wrap', gap: 1 }}>
          {releaseTags.map((tagObj, index) => (
            <Tooltip
              key={index}
              title={
                <Box>
                  <Typography variant="body2">
                    Added by: {tagObj.addedBy?.firstName} {tagObj.addedBy?.lastName} ({tagObj.addedBy?.username})
                  </Typography>
                  <Typography variant="body2">
                    Date: {formatDate(tagObj.dateAdded)}
                  </Typography>
                </Box>
              }
            >
              <Chip
                label={tagObj.tag}
                color="primary"
                variant="outlined"
                size="small"
                deleteIcon={
                  <IconButton size="small" sx={{ '&:hover': { color: 'error.main' } }}>
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                }
                onDelete={() => openDeleteDialog(tagObj.tag)}
                sx={{
                  '& .MuiChip-deleteIcon': {
                    fontSize: '16px'
                  }
                }}
              />
            </Tooltip>
          ))}
        </Stack>

        {/* Add Tag Input */}
        {isAdding && (
          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center', mb: 2 }}>
            <TextField
              size="small"
              placeholder="Enter tag name..."
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  handleAddTag();
                } else if (e.key === 'Escape') {
                  setIsAdding(false);
                  setNewTag('');
                }
              }}
              autoFocus
              disabled={loading}
              sx={{ minWidth: 200 }}
            />
            <Button
              size="small"
              variant="contained"
              onClick={handleAddTag}
              disabled={loading || !newTag.trim()}
            >
              Add
            </Button>
            <Button
              size="small"
              variant="outlined"
              onClick={() => {
                setIsAdding(false);
                setNewTag('');
              }}
              disabled={loading}
            >
              Cancel
            </Button>
          </Box>
        )}

        {releaseTags.length === 0 && !isAdding && (
          <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
            No release tags yet. Click "Add Tag" to create the first one.
          </Typography>
        )}
      </Collapse>

      </Paper>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Remove Release Tag</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to remove the tag "{tagToDelete}"?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={() => handleDeleteTag(tagToDelete)}
            color="error"
            disabled={loading}
          >
            Remove
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default TagManager;
