import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Box,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Divider
} from '@mui/material';
import {
  Folder as FolderIcon,
  Add as AddIcon,
  Assignment as AssignmentIcon
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import axios from 'axios';

const drawerWidth = 240;

const Dashboard = () => {
  const [projects, setProjects] = useState([]);
  const [requirements, setRequirements] = useState([]);
  const { user } = useAuth();
  const navigate = useNavigate();
  const { projectId } = useParams();

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        const res = await axios.get('/api/projects');
        setProjects(res.data);
      } catch (error) {
        console.error('Error fetching projects:', error);
      }
    };

    const fetchRequirements = async () => {
      try {
        if (projectId) {
          const res = await axios.get(`/api/requirements/project/${projectId}`);
          setRequirements(res.data);
        }
      } catch (error) {
        console.error('Error fetching requirements:', error);
      }
    };

    fetchProjects();
    fetchRequirements();
  }, [projectId]);

  const handleProjectClick = (project) => {
    navigate(`/project/${project.elementId || project._id}`);
  };

  const handleRequirementClick = (requirement) => {
    navigate(`/requirement/${requirement.elementId || requirement._id}`);
  };

  return (
    <Box sx={{ display: 'flex' }}>
      <Drawer
        sx={{
          width: drawerWidth,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: drawerWidth,
            boxSizing: 'border-box',
          },
        }}
        variant="permanent"
        anchor="left"
      >
        <Box sx={{ overflow: 'auto', mt: 2 }}>
          <Typography variant="h6" sx={{ px: 2, mb: 2 }}>
            Projects
          </Typography>
          <List>
            {projects.map((project) => (
              <ListItem
                button
                key={project._id}
                onClick={() => handleProjectClick(project)}
              >
                <ListItemIcon>
                  <FolderIcon />
                </ListItemIcon>
                <ListItemText primary={project.name} />
              </ListItem>
            ))}
            <ListItem button onClick={() => navigate('/project/new')}>
              <ListItemIcon>
                <AddIcon />
              </ListItemIcon>
              <ListItemText primary="New Project" />
            </ListItem>
          </List>
        </Box>
      </Drawer>

      <Box component="main" sx={{ flexGrow: 1, p: 3 }}>
        <Typography variant="h4" gutterBottom>
          Welcome, {user?.username}
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Priority Requirements
                </Typography>
                <List>
                  {requirements
                    .filter(req => req.state !== 'Deployed to Customer')
                    .map((requirement) => (
                      <ListItem
                        button
                        key={requirement._id}
                        onClick={() => handleRequirementClick(requirement)}
                      >
                        <ListItemIcon>
                          <AssignmentIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary={requirement.title}
                          secondary={`Version ${requirement.currentVersion} - ${requirement.state}`}
                        />
                      </ListItem>
                    ))}
                </List>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Active Sprint
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle1">Approved</Typography>
                  <List>
                    {requirements
                      .filter(req => req.state === 'Approved')
                      .map((requirement) => (
                        <ListItem
                          button
                          key={requirement._id}
                          onClick={() => handleRequirementClick(requirement)}
                        >
                          <ListItemText primary={requirement.title} />
                        </ListItem>
                      ))}
                  </List>
                </Box>
                <Divider />
                <Box sx={{ mb: 2, mt: 2 }}>
                  <Typography variant="subtitle1">Implementing</Typography>
                  <List>
                    {requirements
                      .filter(req => req.state === 'Implementing')
                      .map((requirement) => (
                        <ListItem
                          button
                          key={requirement._id}
                          onClick={() => handleRequirementClick(requirement)}
                        >
                          <ListItemText primary={requirement.title} />
                        </ListItem>
                      ))}
                  </List>
                </Box>
                <Divider />
                <Box sx={{ mt: 2 }}>
                  <Typography variant="subtitle1">Done Implementing</Typography>
                  <List>
                    {requirements
                      .filter(req => req.state === 'Done Implementing')
                      .map((requirement) => (
                        <ListItem
                          button
                          key={requirement._id}
                          onClick={() => handleRequirementClick(requirement)}
                        >
                          <ListItemText primary={requirement.title} />
                        </ListItem>
                      ))}
                  </List>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
};

export default Dashboard; 