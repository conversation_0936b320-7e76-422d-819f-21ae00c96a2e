import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemAvatar,
  Chip,
  CircularProgress,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Stack,
  Divider,
  Tooltip,
  Collapse
} from '@mui/material';
import {
  Folder as FolderIcon,
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  Check as CheckIcon,
  Save as SaveIcon,
  Logout as LogoutIcon,
  History as HistoryIcon,
  LocalOffer as LocalOfferIcon,
  ExpandMore as ExpandMoreIcon,
  ChevronRight as ChevronRightIcon,
  Group as GroupIcon,
  Chat as ChatIcon,
  Edit as EditIcon
} from '@mui/icons-material';
import { Badge } from '@mui/material';
import Breadcrumbs from '../common/Breadcrumbs';
import { useAuth } from '../../contexts/AuthContext';
import LabelList from '../common/Label';
import LabelDialog from '../requirement/LabelDialog';
import TagManager from '../release/TagManager';
import BulkTagDialog from '../release/BulkTagDialog';
import AddUserDialog from '../common/AddUserDialog';
import EnhancedAvatar from '../common/EnhancedAvatar';
import ElementIdDisplay from '../common/ElementIdDisplay';
import RichTextEditor from '../common/RichTextEditor';
import RichTextDisplayWithLinking from '../common/RichTextDisplayWithLinking';
import TruncatedRichTextDisplay from '../common/TruncatedRichTextDisplay';
import StateTransitionControl from '../common/StateTransitionControl';
import StateWarningDialog from '../common/StateWarningDialog';
import DocumentLabels from '../common/DocumentLabels';
import { stateColors } from '../requirement/RequirementView.styles';

const FeatureView = () => {
  const { featureId } = useParams();
  const navigate = useNavigate();
  const { axios, isAuthenticated, loading: authLoading, logout } = useAuth();
  const [feature, setFeature] = useState(null);
  const [childRequirements, setChildRequirements] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [editMode, setEditMode] = useState(false);
  const [editedTitle, setEditedTitle] = useState('');
  const [editedDescription, setEditedDescription] = useState('');
  const [anchorEl, setAnchorEl] = useState(null);
  const [labels, setLabels] = useState([]);
  const [allLabels, setAllLabels] = useState([]);
  const [labelDialogOpen, setLabelDialogOpen] = useState(false);
  const [newLabelName, setNewLabelName] = useState('');
  const [newElement, setNewElement] = useState({
    type: 'requirement',
    title: '',
    description: '',
    parent: '',
    project: ''
  });
  const [newElementDialogOpen, setNewElementDialogOpen] = useState(false);
  const [addUserDialogOpen, setAddUserDialogOpen] = useState(false);
  const [showVersionHistory, setShowVersionHistory] = useState(false);
  const [selectedVersion, setSelectedVersion] = useState(null);
  const [comment, setComment] = useState('');
  const [versionComment, setVersionComment] = useState('');
  const [usersInChildRequirements, setUsersInChildRequirements] = useState(new Set());
  const [bulkTagDialogOpen, setBulkTagDialogOpen] = useState(false);

  // State management
  const [stateWarningDialog, setStateWarningDialog] = useState({
    open: false,
    title: '',
    message: '',
    onConfirm: null,
    showConfirm: true
  });
  const [userManagementExpanded, setUserManagementExpanded] = useState(true);

  // Comment linking state
  const [linkedTexts, setLinkedTexts] = useState([]);
  const [hoveredLinkId, setHoveredLinkId] = useState(null);
  const [scrollingToComment, setScrollingToComment] = useState(null);
  const commentsContainerRef = useRef(null);

  // Split-view layout state
  const [commentsWidth, setCommentsWidth] = useState(33); // percentage
  const [isResizing, setIsResizing] = useState(false);
  const containerRef = useRef(null);

  // Resizing handlers
  const handleMouseDown = useCallback((e) => {
    setIsResizing(true);
    e.preventDefault();
  }, []);

  const handleMouseMove = useCallback((e) => {
    if (!isResizing || !containerRef.current) return;

    const containerRect = containerRef.current.getBoundingClientRect();
    const newWidth = ((containerRect.right - e.clientX) / containerRect.width) * 100;

    // Limit between 25% and 60%
    const clampedWidth = Math.max(25, Math.min(60, newWidth));
    setCommentsWidth(clampedWidth);
  }, [isResizing]);

  const handleMouseUp = useCallback(() => {
    setIsResizing(false);
  }, []);

  React.useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isResizing, handleMouseMove, handleMouseUp]);

  // Comment linking handlers
  const scrollToComment = useCallback((commentId) => {
    if (!commentsContainerRef.current) return;

    const container = commentsContainerRef.current;
    const commentElement = container.querySelector(`[data-comment-id="${commentId}"]`);

    if (commentElement) {
      // Check if comment is visible within the container
      const containerRect = container.getBoundingClientRect();
      const commentRect = commentElement.getBoundingClientRect();

      const isVisible = (
        commentRect.top >= containerRect.top &&
        commentRect.bottom <= containerRect.bottom
      );

      if (!isVisible) {
        setScrollingToComment(commentId);

        // Calculate the scroll position to center the comment in the container
        const containerScrollTop = container.scrollTop;
        const containerHeight = container.clientHeight;
        const commentOffsetTop = commentElement.offsetTop;
        const commentHeight = commentElement.offsetHeight;

        // Calculate target scroll position to center the comment
        const targetScrollTop = commentOffsetTop - (containerHeight / 2) + (commentHeight / 2);

        // Smooth scroll within the container only
        container.scrollTo({
          top: targetScrollTop,
          behavior: 'smooth'
        });

        // Add a temporary highlight effect
        commentElement.style.transition = 'all 0.5s ease';
        commentElement.style.backgroundColor = 'rgba(25, 118, 210, 0.15)';
        commentElement.style.transform = 'scale(1.02)';
        commentElement.style.boxShadow = '0 4px 20px rgba(25, 118, 210, 0.3)';

        setTimeout(() => {
          commentElement.style.backgroundColor = '';
          commentElement.style.transform = '';
          commentElement.style.boxShadow = '';
          setScrollingToComment(null);
        }, 1500);
      }
    }
  }, []);

  const handleLinkHover = useCallback((linkId, isHovering) => {
    setHoveredLinkId(isHovering ? linkId : null);

    if (linkId && isHovering) {
      // Find the linked comment and scroll to it
      const link = linkedTexts.find(l => l.id === linkId);
      if (link) {
        // Small delay to allow hover effects to start
        setTimeout(() => {
          scrollToComment(link.commentId);
        }, 100);
      }
    }
  }, [linkedTexts, scrollToComment]);

  const handleCreateLink = useCallback(async (newLink) => {
    try {
      // Add version to the link
      const linkWithVersion = {
        ...newLink,
        version: feature.currentVersion
      };

      // Save linked text to database
      await axios.post(`/api/features/${featureId}/linked-texts`, linkWithVersion);

      setLinkedTexts(prev => [...prev, linkWithVersion]);
    } catch (error) {
      console.error('Error creating link:', error);
    }
  }, [featureId, feature, axios]);

  const handleCreateNewComment = useCallback(async (selectedText, selectionRange, commentText) => {
    if (!selectedText || !selectionRange || !commentText.trim()) return;

    try {
      // Create the comment first
      const res = await axios.post(`/api/features/${featureId}/comments`, {
        text: commentText,
        version: feature.currentVersion
      });

      // Update comments state
      setComment('');

      // Create link to new comment
      const newLink = {
        id: `link-${Date.now()}`,
        text: selectedText,
        commentId: res.data._id,
        startOffset: selectionRange.startOffset,
        endOffset: selectionRange.endOffset,
        highlightColor: res.data.user?.color || '#1976d2',
        version: feature.currentVersion
      };

      // Save linked text to database
      await axios.post(`/api/features/${featureId}/linked-texts`, newLink);

      setLinkedTexts(prev => [...prev, newLink]);

      // Refresh feature to get updated comments
      const updatedFeature = await axios.get(`/api/features/${featureId}`);
      setFeature(updatedFeature.data);
    } catch (error) {
      console.error('Error creating comment:', error);
    }
  }, [featureId, feature, axios]);

  const updateUsersInChildRequirements = () => {
    console.log('updateUsersInChildRequirements called with childRequirements:', childRequirements?.length || 0, 'requirements');

    const usersInChildren = new Set();

    if (childRequirements && childRequirements.length > 0) {
      childRequirements.forEach((requirement, index) => {
        console.log(`Requirement ${index}:`, requirement.members?.length || 0, 'members');
        if (requirement.members && requirement.members.length > 0) {
          requirement.members.forEach(member => {
            const userId = typeof member.user === 'object' ? member.user._id : member.user;
            if (userId) {
              usersInChildren.add(userId);
              console.log('Added user to protected list:', userId);
            }
          });
        }
      });
    }

    console.log('Final protected users:', Array.from(usersInChildren));
    setUsersInChildRequirements(usersInChildren);
  };

  useEffect(() => {
    if (authLoading) return;

    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    const fetchFeature = async () => {
      try {
        setLoading(true);
        const response = await axios.get(`/api/features/${featureId}?populate=project`);
        console.log('Fetched feature:', response.data);
        console.log('Project data:', response.data.project);

        // If project is just an ID, fetch the full project data
        if (response.data.project && typeof response.data.project === 'string') {
          const projectRes = await axios.get(`/api/projects/${response.data.project}`);
          response.data.project = projectRes.data;
        }

        setFeature(response.data);
        setEditedTitle(response.data.versions[response.data.versions.length - 1].title);
        setEditedDescription(response.data.versions[response.data.versions.length - 1].description);

        // Fetch all available labels for the dialog
        const labelsRes = await axios.get('/api/labels');
        setAllLabels(labelsRes.data);

        // Get the current version's labels and convert IDs to full objects
        const currentLabels = response.data.versions[response.data.versions.length - 1].labels || [];
        let fullLabels = [];
        if (currentLabels.length > 0) {
          // Check if labels are already populated (objects) or just IDs
          if (typeof currentLabels[0] === 'object' && currentLabels[0]._id) {
            // Labels are already populated
            fullLabels = currentLabels;
          } else {
            // Labels are just IDs, need to map them to full objects
            const labelMap = labelsRes.data.reduce((map, label) => {
              map[label._id] = label;
              return map;
            }, {});
            fullLabels = currentLabels.map(id => labelMap[id]).filter(Boolean);
          }
        }
        setLabels(fullLabels);

        // Load linked texts for the current version
        try {
          const linkedTextsResponse = await axios.get(`/api/features/${featureId}/linked-texts?version=${response.data.currentVersion}`);
          setLinkedTexts(linkedTextsResponse.data || []);
        } catch (linkedTextsError) {
          console.error('Error fetching linked texts:', linkedTextsError);
          setLinkedTexts([]);
        }

        // Fetch child requirements
        const childrenRes = await axios.get(`/api/requirements/${featureId}/children`);
        setChildRequirements(childrenRes.data);

        // Validate and auto-sync feature members with child requirements
        try {
          const validationRes = await axios.post(`/api/features/${featureId}/validate-members`, {
            autoSync: true
          });

          if (validationRes.data.synced && validationRes.data.addedUsers.length > 0) {
            console.log('Auto-synced feature members:', validationRes.data.addedUsers);
            // Update the feature with the synced data, preserving project data
            setFeature(prevFeature => ({
              ...validationRes.data.feature,
              project: prevFeature.project // Preserve the populated project data
            }));

            // Show a notification about the sync
            const addedUserNames = validationRes.data.addedUsers.map(user =>
              user.requirements.length > 0 ? `user from ${user.requirements[0]}` : 'user'
            );

            setStateWarningDialog({
              open: true,
              title: 'Feature Members Updated',
              message: `Added ${validationRes.data.addedUsers.length} user(s) to Feature User Management who were assigned to child requirements: ${addedUserNames.slice(0, 3).join(', ')}${addedUserNames.length > 3 ? ` and ${addedUserNames.length - 3} more` : ''}.`,
              onConfirm: () => setStateWarningDialog(prev => ({ ...prev, open: false })),
              showConfirm: false
            });
          }
        } catch (syncErr) {
          console.warn('Error syncing feature members:', syncErr);
          // Don't fail the whole page load if sync fails
        }

        // Update which users are in child requirements (for delete button visibility)
        // This will be handled by the useEffect that watches childRequirements
      } catch (err) {
        console.error('Error fetching feature:', err);
        if (err.response?.status === 401) {
          navigate('/login');
          return;
        }
        setError(err.response?.data?.message || 'Failed to load feature');
      } finally {
        setLoading(false);
      }
    };

    fetchFeature();
  }, [featureId, axios, isAuthenticated, authLoading, navigate]);

  // Update users in child requirements whenever childRequirements changes
  useEffect(() => {
    // Only update if we have child requirements data
    if (childRequirements && childRequirements.length >= 0) {
      console.log('Child requirements changed, updating users:', childRequirements.length, 'requirements');
      // Small delay to ensure all state updates are complete
      const timeoutId = setTimeout(() => {
        updateUsersInChildRequirements();
      }, 50);

      return () => clearTimeout(timeoutId);
    }
  }, [childRequirements]);

  // Add window focus listener to refresh state when returning to the feature
  useEffect(() => {
    const handleWindowFocus = async () => {
      if (!featureId) return;

      try {
        // Re-fetch child requirements to get latest state
        const childrenRes = await axios.get(`/api/requirements/${featureId}/children`);
        setChildRequirements(childrenRes.data);
        // The useEffect watching childRequirements will handle updating users
      } catch (err) {
        console.warn('Error refreshing child requirements on focus:', err);
      }
    };

    window.addEventListener('focus', handleWindowFocus);

    return () => {
      window.removeEventListener('focus', handleWindowFocus);
    };
  }, [featureId, axios]);

  const handleMenuClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleApprove = async () => {
    try {
      const currentVersion = feature.versions[feature.versions.length - 1];
      const response = await axios.put(`/api/features/${featureId}`, {
        ...currentVersion,
        state: 'Approved'
      });
      setFeature(prevFeature => ({
        ...response.data,
        project: prevFeature.project // Preserve the populated project data
      }));
      handleMenuClose();
    } catch (err) {
      console.error('Error approving feature:', err);
      setError(err.response?.data?.message || 'Failed to approve feature');
    }
  };

  const enterEditMode = () => {
    const currentVersion = feature.versions[feature.versions.length - 1];
    setEditedTitle(currentVersion.title);
    setEditedDescription(currentVersion.description);
    setEditMode(true);
  };

  const cancelEdit = () => {
    const currentVersion = feature.versions[feature.versions.length - 1];
    setEditedTitle(currentVersion.title);
    setEditedDescription(currentVersion.description);
    setEditMode(false);
  };

  const handleSaveChanges = async () => {
    try {
      const currentVersion = feature.versions[feature.versions.length - 1];
      const response = await axios.put(`/api/features/${featureId}`, {
        title: editedTitle,
        description: editedDescription
        // Don't send state or other version properties - let server default to 'New' for new versions
      });
      setFeature(prevFeature => ({
        ...response.data,
        project: prevFeature.project // Preserve the populated project data
      }));
      setEditMode(false);

      // Trigger navigation update if title changed
      if (editedTitle !== currentVersion.title) {
        window.dispatchEvent(new CustomEvent('updateNavigation', {
          detail: {
            type: 'featureTitleUpdated',
            projectId: feature.project._id || feature.project,
            featureId: featureId,
            newTitle: editedTitle
          }
        }));
      }
    } catch (err) {
      console.error('Error updating feature:', err);
      setError(err.response?.data?.message || 'Failed to update feature');
    }
  };

  const handleRemoveLabel = async (labelId) => {
    try {
      const currentVersion = feature.versions[feature.versions.length - 1];
      const updatedLabelIds = currentVersion.labels.filter(id => id !== labelId);
      const response = await axios.put(`/api/features/${featureId}`, {
        title: currentVersion.title,
        description: currentVersion.description,
        labels: updatedLabelIds
        // Don't send state - let server default to 'New' for new versions
      });

      // Convert label IDs to full label objects
      const labelMap = allLabels.reduce((map, label) => {
        map[label._id] = label;
        return map;
      }, {});
      const fullLabels = updatedLabelIds.map(id => labelMap[id]).filter(Boolean);

      setFeature(prevFeature => ({
        ...response.data,
        project: prevFeature.project // Preserve the populated project data
      }));
      setLabels(fullLabels);
    } catch (err) {
      console.error('Error removing label:', err);
      setError(err.response?.data?.message || 'Failed to remove label');
    }
  };

  const handleAddLabel = async (labelId) => {
    console.log('Adding label:', labelId);
    try {
      const currentVersion = feature.versions[feature.versions.length - 1];
      const updatedLabels = [...(currentVersion.labels || []), labelId];
      const response = await axios.put(`/api/features/${featureId}`, {
        title: currentVersion.title,
        description: currentVersion.description,
        labels: updatedLabels
        // Don't send state - let server default to 'New' for new versions
      });
      console.log('Add label response:', response.data);

      // Get the updated labels from the response and convert IDs to full objects
      const newCurrentVersion = response.data.versions[response.data.versions.length - 1];
      const newLabelIds = newCurrentVersion.labels || [];
      console.log('Updated label IDs:', newLabelIds);

      // Convert label IDs to full label objects
      const labelMap = allLabels.reduce((map, label) => {
        map[label._id] = label;
        return map;
      }, {});
      const fullLabels = newLabelIds.map(id => labelMap[id]).filter(Boolean);
      console.log('Full label objects:', fullLabels);

      // Update the feature and labels state, preserving project data
      setFeature(prevFeature => ({
        ...response.data,
        project: prevFeature.project // Preserve the populated project data
      }));
      setLabels(fullLabels);
      setLabelDialogOpen(false);
    } catch (error) {
      console.error('Error adding label:', error);
      setError(error.response?.data?.message || 'Failed to add label');
    }
  };

  const handleCreateLabel = async () => {
    console.log('Creating new label:', newLabelName);
    try {
      const res = await axios.post('/api/labels', { name: newLabelName });
      console.log('Create label response:', res.data);
      setAllLabels([...allLabels, res.data]);
      await handleAddLabel(res.data._id);
      setNewLabelName('');
    } catch (error) {
      console.error('Error creating label:', error);
      setError(error.response?.data?.message || 'Failed to create label');
    }
  };

  const handleCreateElement = async (element) => {
    try {
      // Get project ID - handle both populated and non-populated cases
      const projectId = feature.project?._id || feature.project;

      if (!projectId) {
        console.error('No project ID found in feature:', feature);
        setError('Unable to create requirement: missing project information');
        return;
      }

      console.log('Creating requirement with project ID:', projectId);

      const response = await axios.post('/api/requirements', {
        project: projectId,
        type: element.type,
        feature: feature._id,
        parent: feature._id,
        title: element.title,
        description: element.description
      });

      // After creating the element, fetch it with populated data including the parent feature
      const createdElement = await axios.get(`/api/requirements/${response.data._id}?populate=project,feature`);
      console.log('Created element with populated data:', createdElement.data);

      // Trigger navigation update when requirement is created
      window.dispatchEvent(new CustomEvent('updateNavigation', {
        detail: {
          type: 'requirementCreated',
          projectId: projectId,
          featureId: feature._id,
          requirementId: response.data._id
        }
      }));

      // Navigate to the new requirement
      navigate(`/requirement/${response.data.elementId || response.data._id}`);
    } catch (err) {
      console.error('Error creating element:', err);
      setError(err.response?.data?.message || 'Failed to create element');
    }
  };



  const handleAddUser = async (userId, roles = []) => {
    try {
      const response = await axios.post(`/api/features/${featureId}/members`, {
        userId,
        roles
      });
      setFeature(prevFeature => ({
        ...response.data,
        project: prevFeature.project // Preserve the populated project data
      }));

      // Trigger navigation update when user is added
      window.dispatchEvent(new CustomEvent('updateNavigation', {
        detail: {
          type: 'userAdded',
          projectId: feature.project._id || feature.project,
          userId: userId,
          featureId: featureId
        }
      }));
    } catch (err) {
      console.error('Error adding user:', err);
      setError(err.response?.data?.message || 'Failed to add user to feature');
    }
  };

  const handleRemoveUser = async (userId) => {
    try {
      const response = await axios.delete(`/api/features/${featureId}/members/${userId}`);
      setFeature(prevFeature => ({
        ...response.data,
        project: prevFeature.project // Preserve the populated project data
      }));
    } catch (err) {
      console.error('Error removing user:', err);

      if (err.response?.status === 400 && err.response?.data?.reason === 'User is assigned to child requirements') {
        const assignedRequirements = err.response.data.assignedRequirements || [];
        const requirementsList = assignedRequirements.length > 3
          ? `${assignedRequirements.slice(0, 3).join(', ')} and ${assignedRequirements.length - 3} more`
          : assignedRequirements.join(', ');

        setStateWarningDialog({
          open: true,
          title: 'Cannot Remove User',
          message: `This user cannot be removed from the feature because they are assigned to the following child requirements: ${requirementsList}. Please remove them from these requirements first.`,
          onConfirm: () => setStateWarningDialog(prev => ({ ...prev, open: false })),
          showConfirm: false
        });
      } else {
        setError(err.response?.data?.message || 'Failed to remove user from feature');
      }
    }
  };

  const handleValidateMembers = async () => {
    try {
      // First, refresh child requirements to get latest state
      const childrenRes = await axios.get(`/api/requirements/${featureId}/children`);
      setChildRequirements(childrenRes.data);

      const validationRes = await axios.post(`/api/features/${featureId}/validate-members`, {
        autoSync: false
      });

      // The useEffect watching childRequirements will handle updating users

      if (validationRes.data.missingUsers.length === 0) {
        setStateWarningDialog({
          open: true,
          title: 'Validation Complete',
          message: `All users assigned to child requirements are already in Feature User Management. No action needed.`,
          onConfirm: () => setStateWarningDialog(prev => ({ ...prev, open: false })),
          showConfirm: false
        });
      } else {
        const missingUsersList = validationRes.data.missingUsers.map(user =>
          `User from ${user.requirements[0] || 'requirement'} (${user.roles.join(', ')})`
        );

        setStateWarningDialog({
          open: true,
          title: 'Missing Users Found',
          message: `Found ${validationRes.data.missingUsers.length} user(s) in child requirements who are not in Feature User Management: ${missingUsersList.slice(0, 3).join(', ')}${missingUsersList.length > 3 ? ` and ${missingUsersList.length - 3} more` : ''}. Would you like to add them automatically?`,
          onConfirm: async () => {
            try {
              const syncRes = await axios.post(`/api/features/${featureId}/validate-members`, {
                autoSync: true
              });

              if (syncRes.data.synced) {
                setFeature(prevFeature => ({
                  ...syncRes.data.feature,
                  project: prevFeature.project // Preserve the populated project data
                }));
                // Refresh child requirements after sync
                const refreshedChildrenRes = await axios.get(`/api/requirements/${featureId}/children`);
                setChildRequirements(refreshedChildrenRes.data);
                // The useEffect watching childRequirements will handle updating users

                setStateWarningDialog({
                  open: true,
                  title: 'Users Added Successfully',
                  message: `Added ${syncRes.data.addedUsers.length} user(s) to Feature User Management.`,
                  onConfirm: () => setStateWarningDialog(prev => ({ ...prev, open: false })),
                  showConfirm: false
                });
              }
            } catch (syncErr) {
              console.error('Error syncing users:', syncErr);
              setError('Failed to sync users');
            }
          },
          showConfirm: true
        });
      }
    } catch (err) {
      console.error('Error validating members:', err);
      setError(err.response?.data?.message || 'Failed to validate members');
    }
  };

  const handleTagUpdate = (updatedFeature) => {
    setFeature(prevFeature => ({
      ...updatedFeature,
      project: prevFeature.project // Preserve the populated project data
    }));
  };

  const handleBulkTagSuccess = (result) => {
    // Refresh the feature and child requirements to get updated tags
    const refreshData = async () => {
      try {
        const featureRes = await axios.get(`/api/features/${featureId}?populate=project`);
        setFeature(prevFeature => ({
          ...featureRes.data,
          project: prevFeature.project // Preserve the populated project data
        }));

        const childrenRes = await axios.get(`/api/requirements/${featureId}/children`);
        setChildRequirements(childrenRes.data);
      } catch (err) {
        console.error('Error refreshing data after bulk tag:', err);
      }
    };

    refreshData();
    setBulkTagDialogOpen(false);
  };

  const handleToggleUserManagement = () => {
    setUserManagementExpanded(!userManagementExpanded);
  };

  const handleVersionClick = (version) => {
    // Get all comments for this version from the feature's versions
    const versionComments = feature.versions
      .find(v => v.version === version.version)?.comments || [];

    // Filter comments to only show ones that belong to this version
    const filteredComments = versionComments.filter(comment => comment.version === version.version);

    setSelectedVersion({
      ...version,
      comments: filteredComments.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt))
    });
    setVersionComment('');
  };

  const handleCommentSubmit = async (e) => {
    e.preventDefault();
    if (!comment.trim() || !feature) return;

    try {
      const res = await axios.post(`/api/features/${featureId}/comments`, {
        text: comment,
        version: feature.currentVersion
      });

      // Update the feature with the new comment
      const updatedFeature = await axios.get(`/api/features/${featureId}?populate=project`);
      setFeature(updatedFeature.data);
      setComment('');
    } catch (error) {
      console.error('Error adding comment:', error);
    }
  };

  // State transition functions
  const getValidTransitions = async () => {
    try {
      // For features, we need to implement the logic based on current state
      const currentState = feature.state;
      let next = null;
      let previous = null;

      switch (currentState) {
        case 'NEW':
          next = 'OPEN';
          break;
        case 'OPEN':
          next = 'CLOSED';
          break;
        case 'CLOSED':
          // No transitions from CLOSED
          break;
      }

      // Check if can close (only for OPEN -> CLOSED transition)
      let canTransitionNext = { canTransition: true, reason: null };
      let canTransitionPrevious = { canTransition: false, reason: 'Features cannot move backwards' };

      if (next === 'CLOSED') {
        const response = await axios.get(`/api/features/${featureId}/can-close`);
        canTransitionNext = response.data;
      }

      return {
        currentState,
        next,
        previous,
        canTransitionNext,
        canTransitionPrevious
      };
    } catch (error) {
      console.error('Error fetching valid transitions:', error);
      throw error;
    }
  };

  const handleStateTransition = async (newState, direction) => {
    try {
      // Special handling for closing feature
      if (newState === 'CLOSED') {
        const validation = await axios.get(`/api/features/${featureId}/can-close`);
        if (!validation.data.canClose) {
          setStateWarningDialog({
            open: true,
            title: 'Cannot Close Feature',
            message: `Warning: a Feature can only be closed once all of its child requirements and user stories have been moved to their final state. ${validation.data.reason}`,
            onConfirm: () => setStateWarningDialog(prev => ({ ...prev, open: false })),
            showConfirm: false
          });
          return;
        }
      }

      await axios.post(`/api/features/${featureId}/transition-state`, {
        newState
      });

      // Refresh the feature data
      const response = await axios.get(`/api/features/${featureId}?populate=project`);
      setFeature(response.data);

      // Trigger navigation update
      window.dispatchEvent(new CustomEvent('updateNavigation', {
        detail: {
          type: 'featureStateUpdated',
          projectId: feature.project._id || feature.project,
          featureId: featureId,
          newState: newState
        }
      }));
    } catch (error) {
      console.error('Error transitioning state:', error);

      // Show error dialog
      setStateWarningDialog({
        open: true,
        title: 'State Transition Error',
        message: error.response?.data?.msg || error.response?.data?.reason || 'Failed to transition state',
        onConfirm: () => setStateWarningDialog(prev => ({ ...prev, open: false })),
        showConfirm: false
      });

      throw error;
    }
  };

  const handleVersionCommentSubmit = async () => {
    if (!versionComment.trim() || !selectedVersion) return;

    try {
      const res = await axios.post(`/api/features/${featureId}/comments`, {
        text: versionComment,
        version: selectedVersion.version
      });

      // Update the feature with the new comment
      const updatedFeature = await axios.get(`/api/features/${featureId}?populate=project`);
      setFeature(updatedFeature.data);
      setVersionComment('');

      // Update the selected version with the new comment
      handleVersionClick(selectedVersion);
    } catch (error) {
      console.error('Error adding comment:', error);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography color="error" gutterBottom>Error: {error}</Typography>
        <Button
          onClick={() => navigate(-1)}
          variant="contained"
          sx={{ mt: 2 }}
        >
          Back
        </Button>
      </Box>
    );
  }

  if (!feature) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography gutterBottom>Feature not found</Typography>
        <Button
          onClick={() => navigate(-1)}
          variant="contained"
          sx={{ mt: 2 }}
        >
          Back
        </Button>
      </Box>
    );
  }

  const currentVersion = feature.versions[feature.versions.length - 1];

  return (
    <Box sx={{ p: 3, bgcolor: 'background.default', minHeight: '100vh' }}>
      <Breadcrumbs
        items={[
          {
            label: 'Projects',
            path: '/',
            icon: FolderIcon
          },
          ...(feature.project && (feature.project._id || (typeof feature.project === 'string' && feature.project !== 'undefined')) ? [{
            label: feature.project?.name || 'Project',
            path: `/project/${feature.project._id || feature.project}`,
            icon: FolderIcon
          }] : []),
          {
            label: currentVersion.title,
            path: `/feature/${featureId}`,
            icon: FolderIcon
          }
        ]}
      />

      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 3 }}>
        <Box>
          {editMode ? (
            <>
              <Typography variant="h4" component="div" gutterBottom>
                <input
                  type="text"
                  value={editedTitle}
                  onChange={(e) => setEditedTitle(e.target.value)}
                  style={{ width: '100%', fontSize: '2rem', padding: '0.5rem' }}
                />
              </Typography>

            </>
          ) : (
            <>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <ElementIdDisplay elementId={feature.elementId} />
                <Typography variant="h4" component="div">
                  {currentVersion.title}
                  <Tooltip title="Requirement and User Story changes don't affect a Feature's version - only title and description changes do. Requirement additions and removals often indicate a new Feature is necessary.">
                    <Chip
                      label={`v${feature.currentVersion}`}
                      size="small"
                      icon={<HistoryIcon />}
                      onClick={() => {
                        setShowVersionHistory(true);
                        handleVersionClick(currentVersion);
                      }}
                      sx={{ ml: 1, mr: 3 }}
                    />
                  </Tooltip>
                </Typography>
                <StateTransitionControl
                  currentState={feature.state}
                  onTransition={handleStateTransition}
                  getValidTransitions={getValidTransitions}
                  disabled={editMode}
                  size="small"
                />
              </Box>
              {/* Labels row */}
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap', mb: 2 }}>
                <Chip
                  label="Add Label"
                  icon={<AddIcon />}
                  onClick={() => setLabelDialogOpen(true)}
                  variant="outlined"
                  color="primary"
                  size="small"
                  sx={{
                    flexShrink: 0,
                    '&:hover': {
                      borderColor: 'primary.main',
                      backgroundColor: 'rgba(25, 118, 210, 0.1)',
                      boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)'
                    }
                  }}
                />
                <LabelList labels={labels} onDelete={handleRemoveLabel} />
              </Box>

            </>
          )}
        </Box>

        <Box>
          {editMode ? (
            <>
              <Button
                variant="contained"
                startIcon={<SaveIcon />}
                onClick={handleSaveChanges}
                sx={{ mr: 1 }}
              >
                Save
              </Button>
              <Button
                variant="outlined"
                onClick={cancelEdit}
              >
                Cancel
              </Button>
            </>
          ) : (
            <IconButton onClick={handleMenuClick}>
              <MoreVertIcon />
            </IconButton>
          )}
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
          >
            <MenuItem onClick={() => {
              enterEditMode();
              handleMenuClose();
            }}>
              <EditIcon sx={{ mr: 1 }} />
              Edit
            </MenuItem>
            <MenuItem onClick={() => {
              setNewElement({
                type: 'requirement',
                title: '',
                description: '',
                parent: feature._id,
                project: feature.project._id
              });
              setNewElementDialogOpen(true);
              handleMenuClose();
            }}>
              <AddIcon sx={{ mr: 1 }} />
              Add New Element
            </MenuItem>
            <MenuItem onClick={handleApprove}>
              <CheckIcon sx={{ mr: 1 }} />
              Approve
            </MenuItem>
          </Menu>
        </Box>
      </Box>

      {/* Split View Layout: Description and Comments */}
      <Box ref={containerRef} sx={{ display: 'flex', gap: 3, position: 'relative', mb: 3 }}>
        {/* Left: Description */}
        <Paper sx={{
          flex: 1,
          width: `${100 - commentsWidth}%`,
          p: 3,
          borderRadius: 3,
          boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
          border: '1px solid',
          borderColor: 'divider',
          transition: isResizing ? 'none' : 'width 0.2s ease'
        }}>
          <Typography variant="h6" sx={{ mb: 2 }}>Description</Typography>
          {editMode ? (
            <RichTextEditor
              value={editedDescription}
              onChange={setEditedDescription}
              placeholder="Enter description..."
              height={200}
              disabled={false}
            />
          ) : (
            <RichTextDisplayWithLinking
              content={currentVersion.description}
              comments={feature?.versions?.flatMap(v => v.comments || []) || []}
              linkedTexts={linkedTexts}
              onCreateLink={handleCreateLink}
              onCreateNewComment={handleCreateNewComment}
              onLinkHover={handleLinkHover}
              hoveredLinkId={hoveredLinkId}
              scrollToComment={scrollToComment}
            />
          )}
        </Paper>

        {/* Resize Handle */}
        <Box
          onMouseDown={handleMouseDown}
          sx={{
            width: '4px',
            cursor: 'col-resize',
            backgroundColor: isResizing ? 'primary.main' : 'divider',
            '&:hover': {
              backgroundColor: 'primary.main'
            },
            transition: 'background-color 0.2s ease',
            borderRadius: 1
          }}
        />

        {/* Right: Comments */}
        <Paper sx={{
          width: `${commentsWidth}%`,
          minWidth: '300px',
          maxHeight: '80vh',
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
          p: 3,
          borderRadius: 3,
          transition: isResizing ? 'none' : 'width 0.2s ease'
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
              <ChatIcon sx={{ mr: 1, color: '#1976d2' }} />
              Comments
            </Typography>
            <Badge badgeContent={currentVersion.comments?.filter(c => c.version === feature.currentVersion).length || 0} color="primary" />
          </Box>
          <Box ref={commentsContainerRef} sx={{ flex: 1, overflow: 'auto', mb: 2 }}>
            <List>
              {currentVersion.comments && currentVersion.comments.length > 0 ? (
                currentVersion.comments
                  .filter(comment => comment.version === feature.currentVersion)
                  .sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt))
                  .map((comment) => (
                    <ListItem
                      key={comment._id}
                      data-comment-id={comment._id}
                      sx={{
                        borderRadius: 1,
                        mb: 1,
                        transition: 'all 0.2s ease',
                        backgroundColor: hoveredLinkId && linkedTexts.some(lt => lt.commentId === comment._id && lt.id === hoveredLinkId)
                          ? `${comment.user?.color || '#1976d2'}15`
                          : 'transparent',
                        border: scrollingToComment === comment._id
                          ? `2px solid ${comment.user?.color || '#1976d2'}`
                          : '2px solid transparent',
                        '&:hover': {
                          backgroundColor: 'action.hover'
                        }
                      }}
                    >
                      <ListItemAvatar>
                        <EnhancedAvatar
                          user={comment.user}
                          size={32}
                        />
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {comment.user?.firstName} {comment.user?.lastName}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              (v{comment.version})
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {new Date(comment.createdAt).toLocaleDateString()}
                            </Typography>
                          </Box>
                        }
                        secondary={
                          <Typography variant="body2" sx={{ mt: 0.5 }}>
                            {comment.text}
                          </Typography>
                        }
                      />
                    </ListItem>
                  ))
              ) : (
                <Typography variant="body2" color="text.secondary" sx={{ p: 2 }}>
                  No comments yet
                </Typography>
              )}
            </List>
          </Box>
          <Box sx={{ mt: 2 }}>
            <TextField
              fullWidth
              multiline
              rows={3}
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="Add a comment..."
              variant="outlined"
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  '&:hover fieldset': {
                    borderColor: 'primary.main',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: 'primary.main',
                  }
                }
              }}
            />
            <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                variant="contained"
                color="primary"
                startIcon={<AddIcon />}
                onClick={handleCommentSubmit}
                disabled={!comment.trim()}
                sx={{
                  borderRadius: 2,
                  textTransform: 'none',
                  fontWeight: 600,
                  mt: 1,
                  px: 3,
                  py: 1
                }}
              >
                Add Comment
              </Button>
            </Box>
          </Box>
        </Paper>
      </Box>

      {/* Main Content Section */}
      <Box sx={{ display: 'flex', gap: 3 }}>
        {/* Left: Requirements and other content */}
        <Box sx={{ flex: 1 }}>
          {childRequirements.length > 0 && (
            <Paper sx={{
              p: 3,
              mb: 3,
              borderRadius: 3,
              boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
              border: '1px solid',
              borderColor: 'divider'
            }}>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>Child Requirements</Typography>
              <List>
                {childRequirements.map(child => {
                  const childVersion = child.versions[child.versions.length - 1];
                  return (
                    <ListItem
                      key={child._id}
                      button
                      onClick={() => navigate(`/requirement/${child.elementId || child._id}`)}
                    >
                      <ListItemIcon>
                        <FolderIcon />
                      </ListItemIcon>
                      <ListItemText
                        primary={childVersion.title}
                        secondary={
                          <TruncatedRichTextDisplay
                            content={childVersion.description}
                            title={`${childVersion.title} - Description`}
                            sx={{
                              fontSize: '0.875rem',
                              color: 'text.secondary',
                              '& p': { margin: '0.25em 0' }
                            }}
                          />
                        }
                      />
                      <Chip
                        label={childVersion.state}
                        color={getStateColor(childVersion.state)}
                        size="small"
                        sx={{ ml: 1 }}
                      />
                    </ListItem>
                  );
                })}
              </List>
            </Paper>
          )}

          {/* User Management Section */}
          <Paper sx={{
            p: 3,
            mb: 3,
            borderRadius: 3,
            boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
            border: '1px solid',
            borderColor: 'divider'
          }}>
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                mb: 2,
                cursor: 'pointer'
              }}
              onClick={handleToggleUserManagement}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <IconButton size="small" sx={{ p: 0 }}>
                  {userManagementExpanded ? <ExpandMoreIcon /> : <ChevronRightIcon />}
                </IconButton>
                <GroupIcon sx={{ color: 'primary.main' }} />
                <Typography variant="h6" sx={{ fontWeight: 600 }}>User Management</Typography>
                {!userManagementExpanded && (
                  <Chip
                    label={`${feature?.members?.length || 0} member${(feature?.members?.length || 0) !== 1 ? 's' : ''}`}
                    size="small"
                    variant="outlined"
                    color="primary"
                  />
                )}
              </Box>
              {userManagementExpanded && (
                <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                  <Tooltip title="Check if any users have been added to child requirements but are missing from Feature User Management">
                    <Chip
                      label="Validate"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleValidateMembers();
                      }}
                      variant="outlined"
                      color="primary"
                      size="small"
                      sx={{
                        flexShrink: 0,
                        '&:hover': {
                          borderColor: 'primary.main',
                          backgroundColor: 'rgba(25, 118, 210, 0.1)',
                          boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)'
                        }
                      }}
                    />
                  </Tooltip>
                  <Chip
                    label="Add User"
                    icon={<AddIcon />}
                    onClick={(e) => {
                      e.stopPropagation();
                      setAddUserDialogOpen(true);
                    }}
                    variant="outlined"
                    color="primary"
                    size="small"
                    sx={{
                      flexShrink: 0,
                      '&:hover': {
                        borderColor: 'primary.main',
                        backgroundColor: 'rgba(25, 118, 210, 0.1)',
                        boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)'
                      }
                    }}
                  />
                </Box>
              )}
            </Box>
            <Collapse in={userManagementExpanded} timeout="auto" unmountOnExit>
              <Divider sx={{ mb: 2 }} />
              <Stack direction="row" flexWrap="wrap" spacing={1}>
              {feature?.members?.map(member => {
                // Handle both populated and non-populated user fields
                const user = member.user;
                if (!user) return null;

                // Handle case where user might be just an ID string
                const userId = typeof user === 'object' ? user._id : user;
                const username = typeof user === 'object' ? user.username : 'Unknown User';
                const userColor = typeof user === 'object' ? user.color : '#1976d2';

                // Check if user is assigned to child requirements
                const isInChildRequirements = usersInChildRequirements.has(userId);

                const chipElement = (
                  <Chip
                    key={userId}
                    label={username}
                    avatar={<EnhancedAvatar user={user} size={24} />}
                    sx={{
                      bgcolor: userColor + '20',
                      m: 0.5,
                      // Hide delete icon for users who can't be deleted
                      ...(isInChildRequirements && {
                        '& .MuiChip-deleteIcon': {
                          display: 'none'
                        }
                      })
                    }}
                    onDelete={isInChildRequirements ? undefined : () => handleRemoveUser(userId)}
                  />
                );

                // Wrap with tooltip if user can't be deleted
                return isInChildRequirements ? (
                  <Tooltip
                    key={userId}
                    title="This user is assigned to child requirements and cannot be removed from the feature"
                    arrow
                  >
                    <span>{chipElement}</span>
                  </Tooltip>
                ) : chipElement;
              }).filter(Boolean) || []}
              </Stack>
            </Collapse>
          </Paper>

          {/* Release Tags Section */}
          <Box sx={{ mb: 3 }}>
            <TagManager
              item={feature}
              itemType="feature"
              onUpdate={handleTagUpdate}
              bulkTagButton={childRequirements.length > 0 && (
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => setBulkTagDialogOpen(true)}
                  sx={{
                    '&:hover': {
                      borderColor: 'primary.main',
                      backgroundColor: 'rgba(25, 118, 210, 0.1)',
                      boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)'
                    }
                  }}
                >
                  Bulk Apply Tags
                </Button>
              )}
            />
          </Box>

          {/* Document History Section */}
          <DocumentLabels
            element={feature}
            elementType="feature"
            projectId={feature.project._id || feature.project}
            isCollapsible={true}
          />
        </Box>

        {/* Resize Handle */}
        <Box
          onMouseDown={handleMouseDown}
          sx={{
            width: 8,
            cursor: 'col-resize',
            backgroundColor: 'divider',
            borderRadius: 1,
            opacity: isResizing ? 1 : 0.3,
            transition: 'opacity 0.2s ease',
            '&:hover': {
              opacity: 1,
              backgroundColor: 'primary.main'
            },
            position: 'relative',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              width: 3,
              height: 20,
              backgroundColor: 'currentColor',
              borderRadius: 1
            }
          }}
        />

      </Box>

      <Dialog
        open={newElementDialogOpen}
        onClose={() => setNewElementDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Add New Element to Feature</DialogTitle>
        <DialogContent>
          <TextField
            select
            label="Type"
            value={newElement.type}
            onChange={(e) => {
              const type = e.target.value;
              setNewElement({
                ...newElement,
                type,
                parent: feature._id,
                project: feature.project._id
              });
            }}
            fullWidth
            margin="normal"
          >
            <MenuItem value="requirement">Requirement</MenuItem>
            <MenuItem value="user_story">User Story</MenuItem>
          </TextField>
          <TextField
            label="Title"
            value={newElement.title}
            onChange={(e) => setNewElement({ ...newElement, title: e.target.value })}
            fullWidth
            margin="normal"
            required
          />
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" sx={{ mb: 1 }}>Description *</Typography>
            <RichTextEditor
              value={newElement.description}
              onChange={(value) => setNewElement({ ...newElement, description: value })}
              placeholder="Enter description..."
              height={150}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setNewElementDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={() => handleCreateElement(newElement)}
            variant="contained"
            disabled={!newElement.title || !newElement.description}
          >
            Submit
          </Button>
        </DialogActions>
      </Dialog>

      <AddUserDialog
        open={addUserDialogOpen}
        onClose={() => setAddUserDialogOpen(false)}
        onAdd={handleAddUser}
        existingUsers={feature?.members || []}
        title="Add User to Feature"
      />

      {/* Version History Dialog */}
      <Dialog
        open={showVersionHistory}
        onClose={() => {
          setShowVersionHistory(false);
          setSelectedVersion(null);
          setVersionComment('');
        }}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Version History</DialogTitle>
        <DialogContent>
          <List>
            {feature.versions.map((version) => (
              <ListItem
                key={version.version}
                button
                onClick={() => handleVersionClick(version)}
                selected={selectedVersion?.version === version.version}
              >
                <ListItemText
                  primary={`Version ${version.version}`}
                  secondary={version.createdAt ? new Date(version.createdAt).toLocaleString() : 'Unknown date'}
                />
              </ListItem>
            ))}
          </List>

          {selectedVersion && (
            <Box sx={{ mt: 2, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
              <Typography variant="h6" gutterBottom>
                Version {selectedVersion.version} Details
              </Typography>

              <Typography variant="subtitle1" gutterBottom>
                Title: {selectedVersion.title}
              </Typography>

              <Typography variant="subtitle1" gutterBottom>
                Description:
              </Typography>
              <RichTextDisplayWithLinking
                content={selectedVersion.description}
                comments={selectedVersion.comments || []}
                linkedTexts={linkedTexts.filter(lt => lt.version === selectedVersion.version)}
                onCreateLink={handleCreateLink}
                onCreateNewComment={handleCreateNewComment}
                onLinkHover={handleLinkHover}
                hoveredLinkId={hoveredLinkId}
                scrollToComment={scrollToComment}
              />

              <Typography variant="body2" gutterBottom>
                State: {selectedVersion.state}
              </Typography>

              {/* Release Tags */}
              {selectedVersion.releaseTags && selectedVersion.releaseTags.length > 0 && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>Release Tags:</Typography>
                  <Stack direction="row" spacing={1} flexWrap="wrap">
                    {selectedVersion.releaseTags.map((tagObj, index) => (
                      <Chip
                        key={index}
                        label={tagObj.tag}
                        size="small"
                        icon={<LocalOfferIcon />}
                        color="secondary"
                        title={`Added by ${tagObj.addedBy?.username || 'Unknown'} on ${new Date(tagObj.dateAdded).toLocaleString()}`}
                      />
                    ))}
                  </Stack>
                </Box>
              )}

              {selectedVersion.changes && selectedVersion.changes.length > 0 && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="subtitle1" gutterBottom>
                    Changes in this version:
                  </Typography>
                  {selectedVersion.changes.map((change, index) => (
                    <Typography key={index} variant="body2" sx={{ ml: 2 }}>
                      • {change.field}: "{change.from}" → "{change.to}"
                    </Typography>
                  ))}
                </Box>
              )}

              <Box sx={{ mt: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Comments
                </Typography>
                {selectedVersion.comments && selectedVersion.comments.length > 0 ? (
                  selectedVersion.comments.map((comment) => (
                    <Box key={comment._id} sx={{ mb: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Typography variant="body2">
                          {comment.text}
                          <Chip
                            size="small"
                            label={`v${comment.version}`}
                            icon={<HistoryIcon />}
                            sx={{ ml: 1 }}
                          />
                        </Typography>
                      </Box>
                      <Typography variant="caption" color="text.secondary">
                        {new Date(comment.createdAt).toLocaleString()} by {comment.user?.username || 'Unknown'}
                      </Typography>
                    </Box>
                  ))
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    No comments for this version
                  </Typography>
                )}

                <Box sx={{ mt: 2 }}>
                  <TextField
                    fullWidth
                    multiline
                    rows={3}
                    value={versionComment}
                    onChange={(e) => setVersionComment(e.target.value)}
                    placeholder="Add a comment about this version..."
                    variant="outlined"
                  />
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleVersionCommentSubmit}
                    sx={{ mt: 1 }}
                  >
                    Add Comment
                  </Button>
                </Box>
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => {
            setShowVersionHistory(false);
            setSelectedVersion(null);
            setVersionComment('');
          }}>
            Close
          </Button>
        </DialogActions>
      </Dialog>

      <LabelDialog
        open={labelDialogOpen}
        onClose={() => {
          setLabelDialogOpen(false);
          setNewLabelName('');
        }}
        allLabels={allLabels}
        labels={labels}
        onAddLabel={handleAddLabel}
        newLabelName={newLabelName}
        setNewLabelName={setNewLabelName}
        onCreateLabel={handleCreateLabel}
      />

      {/* Bulk Tag Dialog */}
      <BulkTagDialog
        open={bulkTagDialogOpen}
        onClose={() => setBulkTagDialogOpen(false)}
        feature={feature}
        childRequirements={childRequirements}
        onSuccess={handleBulkTagSuccess}
      />

      {/* State Warning Dialog */}
      <StateWarningDialog
        open={stateWarningDialog.open}
        onClose={() => setStateWarningDialog(prev => ({ ...prev, open: false }))}
        onConfirm={stateWarningDialog.onConfirm}
        title={stateWarningDialog.title}
        message={stateWarningDialog.message}
        showConfirm={stateWarningDialog.showConfirm !== false}
      />
    </Box>
  );
};

const getStateColor = (state) => {
  // Use shared state colors for consistency
  return stateColors[state] || 'default';
};

export default FeatureView;