import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Paper,
  Grid,
  CircularProgress
} from '@mui/material';
import { Folder as FolderIcon, Add as AddIcon } from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';

const ProjectList = () => {
  const navigate = useNavigate();
  const { isAuthenticated, axios } = useAuth();
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setLoading(true);
        setError(null);
        console.log('Fetching projects...');
        
        const response = await axios.get('/api/projects');
        
        console.log('Projects response:', response.data);
        setProjects(response.data);
      } catch (error) {
        console.error('Error fetching projects:', error);
        setError(error.response?.data?.message || error.message || 'Failed to load projects');
      } finally {
        setLoading(false);
      }
    };

    if (isAuthenticated) {
      fetchProjects();
    }
  }, [isAuthenticated, axios]);

  if (!isAuthenticated) {
    return null; // The PrivateRoute will handle the redirect
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography color="error">Error: {error}</Typography>
        <Button onClick={() => navigate('/login')} sx={{ mt: 2 }}>
          Back to Login
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        mb: 3,
        mt: 8  // Add top margin to move content below avatar
      }}>
        <Typography variant="h4">Projects</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => navigate('/project/new')}
        >
          New Project
        </Button>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            {projects.length === 0 ? (
              <Typography variant="body1" color="text.secondary" sx={{ p: 2 }}>
                No projects found. Create a new project to get started.
              </Typography>
            ) : (
              <List>
                {projects.map((project) => (
                  <ListItem
                    key={project._id}
                    button
                    onClick={() => navigate(`/project/${project.elementId || project._id}`)}
                  >
                    <ListItemIcon>
                      <FolderIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary={project.name}
                      secondary={project.description}
                    />
                  </ListItem>
                ))}
              </List>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ProjectList; 