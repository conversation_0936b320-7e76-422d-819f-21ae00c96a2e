// Version History Dialog Component
// Displays version history and allows adding comments to specific versions

import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  List,
  ListItem,
  ListItemText,
  Box,
  Typography,
  TextField,
  Chip,
  Stack,
  Divider
} from '@mui/material';
import { History as HistoryIcon, LocalOffer as TagIcon } from '@mui/icons-material';
import ApprovalBubble from '../common/ApprovalBubble';
import RichTextDisplay from '../common/RichTextDisplay';

const VersionHistoryDialog = ({
  open,
  onClose,
  requirement,
  selectedVersion,
  onVersionClick,
  versionComment,
  setVersionComment,
  onVersionCommentSubmit
}) => {
  if (!requirement) return null;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>Version History</DialogTitle>
      <DialogContent>
        <List>
          {requirement.versions.map((version) => (
            <ListItem
              key={version.version}
              button
              onClick={() => onVersionClick(version)}
              selected={selectedVersion?.version === version.version}
            >
              <ListItemText
                primary={`Version ${version.version}`}
                secondary={version.createdAt ? new Date(version.createdAt).toLocaleString() : 'Unknown date'}
              />
            </ListItem>
          ))}
        </List>

        {selectedVersion && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="h6" gutterBottom>
              Version {selectedVersion.version} Details
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Created: {selectedVersion.createdAt ? new Date(selectedVersion.createdAt).toLocaleString() : 'Unknown date'}
            </Typography>

            {/* Title */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>Title:</Typography>
              <Typography variant="body1">{selectedVersion.title}</Typography>
            </Box>

            {/* State */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>State:</Typography>
              <Chip
                label={selectedVersion.state || 'New'}
                size="small"
                color="primary"
                variant="outlined"
              />
            </Box>

            {/* Release Tags */}
            {selectedVersion.releaseTags && selectedVersion.releaseTags.length > 0 && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>Release Tags:</Typography>
                <Stack direction="row" spacing={1} flexWrap="wrap">
                  {selectedVersion.releaseTags.map((tagObj, index) => (
                    <Chip
                      key={index}
                      label={tagObj.tag}
                      size="small"
                      icon={<TagIcon />}
                      color="secondary"
                      title={`Added by ${tagObj.addedBy?.username || 'Unknown'} on ${new Date(tagObj.dateAdded).toLocaleString()}`}
                    />
                  ))}
                </Stack>
              </Box>
            )}

            {/* Description */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>Description:</Typography>
              <RichTextDisplay content={selectedVersion.description} />
            </Box>

            {/* Changes */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                {selectedVersion.version === 1 ? 'Initial Version' : 'Changes:'}
              </Typography>
              {selectedVersion.version === 1 ? (
                <Typography variant="body2" color="text.secondary">
                  This is the initial version of the requirement.
                </Typography>
              ) : selectedVersion.changes && selectedVersion.changes.length > 0 ? (
                <Box>
                  {selectedVersion.changes.map((change, index) => (
                    <Box key={index} sx={{ ml: 2, mb: 1 }}>
                      <Typography variant="body2">
                        {change.field.charAt(0).toUpperCase() + change.field.slice(1)}:
                      </Typography>
                      <Typography variant="body2" color="error" sx={{ ml: 2, textDecoration: 'line-through' }}>
                        {change.from}
                      </Typography>
                      <Typography variant="body2" color="success.main" sx={{ ml: 2 }}>
                        {change.to}
                      </Typography>
                    </Box>
                  ))}
                </Box>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No changes recorded for this version.
                </Typography>
              )}
            </Box>

            <Divider sx={{ my: 2 }} />

            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle1" gutterBottom>
                Approvers
              </Typography>
              {requirement?.approvers && requirement.approvers.length > 0 ? (
                <Stack direction="row" flexWrap="wrap" spacing={1} sx={{ mb: 2 }}>
                  {requirement.approvers.map((approver) => {
                    const approverUser = approver.user;
                    const userId = typeof approverUser === 'object' ? approverUser._id : approverUser;

                    // Check if this approver has approved this version
                    const hasApproved = requirement?.approvals?.some(
                      approval => (typeof approval.user === 'object' ? approval.user._id : approval.user) === userId &&
                                 approval.version === selectedVersion.version
                    ) || false;

                    return (
                      <ApprovalBubble
                        key={`approver-${userId}`}
                        user={approverUser}
                        isApprover={true}
                        isApproved={hasApproved}
                        canToggleApproval={false} // Read-only in version popup
                        roles={[]} // No roles needed for approver display
                      />
                    );
                  })}
                </Stack>
              ) : (
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  No approvers for this requirement
                </Typography>
              )}
            </Box>

            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle1" gutterBottom>
                Comments
              </Typography>
              {selectedVersion.comments && selectedVersion.comments.length > 0 ? (
                selectedVersion.comments.map((comment) => (
                  <Box key={comment._id} sx={{ mb: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Typography variant="body2">
                        {comment.text}
                        <Chip
                          size="small"
                          label={`v${comment.version}`}
                          icon={<HistoryIcon />}
                          sx={{ ml: 1 }}
                        />
                      </Typography>
                    </Box>
                    <Typography variant="caption" color="text.secondary">
                      {new Date(comment.createdAt).toLocaleString()} by {comment.user?.username || 'Unknown'}
                    </Typography>
                  </Box>
                ))
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No comments for this version
                </Typography>
              )}

              <Box sx={{ mt: 2 }}>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  value={versionComment}
                  onChange={(e) => setVersionComment(e.target.value)}
                  placeholder="Add a comment about this version..."
                  variant="outlined"
                />
                <Button
                  variant="contained"
                  color="primary"
                  onClick={onVersionCommentSubmit}
                  sx={{ mt: 1 }}
                >
                  Add Comment
                </Button>
              </Box>
            </Box>
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default VersionHistoryDialog;
