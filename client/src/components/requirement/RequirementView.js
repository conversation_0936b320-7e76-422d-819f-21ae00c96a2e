import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Button,
  List,
  ListItem,
  ListItemText,
  TextField,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Divider,
  IconButton,
  Menu,
  MenuItem,
  ListItemAvatar,
  ListItemIcon,
  Select,
  FormControl,
  FormControlLabel,
  InputLabel,
  Stack,
  Tooltip,
  CircularProgress,
  Checkbox,
  Collapse,
  Badge
} from '@mui/material';
import {
  History as HistoryIcon,
  MoreVert as MoreVertIcon,
  Person as PersonIcon,
  Folder as FolderIcon,
  Assignment as AssignmentIcon,
  Save as SaveIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Label as LabelIcon,
  Edit as EditIcon,
  Done as DoneIcon,
  ExpandMore as ExpandMoreIcon,
  ChevronRight as ChevronRightIcon,
  Group as GroupIcon,
  Chat as ChatIcon,
  DragIndicator as DragIcon
} from '@mui/icons-material';
import axios from 'axios';
import socketManager from '../../utils/socket';
import Breadcrumbs from '../common/Breadcrumbs';
import LabelList from '../common/Label';
import { useAuth } from '../../contexts/AuthContext';
import UserBubble from '../common/UserBubble';
import ApprovalBubble from '../common/ApprovalBubble';
import AddUserDialog from '../common/AddUserDialog';
import EnhancedAvatar from '../common/EnhancedAvatar';
import ElementIdDisplay from '../common/ElementIdDisplay';
import StateTransitionControl from '../common/StateTransitionControl';
import StateWarningDialog from '../common/StateWarningDialog';
import {
  stateColors,
  paperStyles,
  layoutStyles,
  collapsibleStyles,
  titleInputStyles,
  commentStyles,
  typographyStyles,
  userManagementStyles,
  taskStyles,
  labelRowStyles,
  versionHistoryStyles
} from './RequirementView.styles';
import {
  MainContainer,
  StandardPaper,
  HeaderRow,
  HeaderLeft,
  EditButtonGroup,
  NeumorphicButton,
  LabelRowContainer,
  SectionHeader,
  CommentForm
} from './RequirementView.styled';
import VersionHistoryDialog from './VersionHistoryDialog';
import LabelDialog from './LabelDialog';
import TaskDialog from './TaskDialog';
import RichTextEditor from '../common/RichTextEditor';
import RichTextDisplay from '../common/RichTextDisplay';
import RichTextDisplayWithLinking from '../common/RichTextDisplayWithLinking';
import TestManagement from '../test/TestManagement';
import TagManager from '../release/TagManager';
import DocumentLabels from '../common/DocumentLabels';

const ROLES = [
  'developer',
  'product_manager',
  'project_manager',
  'tester'
];

const RequirementView = () => {
  const { requirementId } = useParams();
  const navigate = useNavigate();
  const { axios, isAuthenticated, loading: authLoading, user } = useAuth();
  const [requirement, setRequirement] = useState(null);
  const [comment, setComment] = useState('');
  const [versionDialogOpen, setVersionDialogOpen] = useState(false);
  const [selectedVersion, setSelectedVersion] = useState(null);
  const [anchorEl, setAnchorEl] = useState(null);
  const [editMode, setEditMode] = useState(false);
  const [editedTitle, setEditedTitle] = useState('');
  const [editedDescription, setEditedDescription] = useState('');
  const socket = socketManager.getSocket();
  const [comments, setComments] = useState([]);
  const [newElementDialogOpen, setNewElementDialogOpen] = useState(false);
  const [newElement, setNewElement] = useState({
    type: 'requirement',
    title: '',
    description: '',
    parent: null
  });
  const [childRequirements, setChildRequirements] = useState([]);
  const [parentFeature, setParentFeature] = useState(null);
  const [versionComment, setVersionComment] = useState('');
  const [showVersionHistory, setShowVersionHistory] = useState(false);
  const [labels, setLabels] = useState([]);
  const [allLabels, setAllLabels] = useState([]);
  const [labelDialogOpen, setLabelDialogOpen] = useState(false);
  const [newLabelName, setNewLabelName] = useState('');
  const [users, setUsers] = useState([]);
  const [selectedUser, setSelectedUser] = useState('');
  const [taskText, setTaskText] = useState('');
  const [selectedRole, setSelectedRole] = useState('');
  const [editTask, setEditTask] = useState(null);
  const [taskDialogOpen, setTaskDialogOpen] = useState(false);
  const [editTaskDialogOpen, setEditTaskDialogOpen] = useState(false);
  const [editingTask, setEditingTask] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [addUserDialogOpen, setAddUserDialogOpen] = useState(false);
  const [newTaskDialogOpen, setNewTaskDialogOpen] = useState(false);
  const [newTask, setNewTask] = useState({
    text: '',
    role: ''
  });
  const [editingTaskId, setEditingTaskId] = useState(null);
  const [editingTaskText, setEditingTaskText] = useState('');
  const [isApprover, setIsApprover] = useState(true); // For task dialog approver checkbox
  const [userManagementExpanded, setUserManagementExpanded] = useState(true); // For collapsible user management section

  // State management
  const [stateWarningDialog, setStateWarningDialog] = useState({
    open: false,
    title: '',
    message: '',
    onConfirm: null,
    showConfirm: true
  });

  // Split-view layout state
  const [commentsWidth, setCommentsWidth] = useState(33); // percentage
  const [isResizing, setIsResizing] = useState(false);
  const containerRef = useRef(null);

  // Comment linking state
  const [linkedTexts, setLinkedTexts] = useState([]);
  const [hoveredLinkId, setHoveredLinkId] = useState(null);
  const [scrollingToComment, setScrollingToComment] = useState(null);
  const commentsContainerRef = useRef(null);

  // Resizing handlers
  const handleMouseDown = useCallback((e) => {
    setIsResizing(true);
    e.preventDefault();
  }, []);

  const handleMouseMove = useCallback((e) => {
    if (!isResizing || !containerRef.current) return;

    const containerRect = containerRef.current.getBoundingClientRect();
    const newWidth = ((containerRect.right - e.clientX) / containerRect.width) * 100;

    // Limit between 25% and 60%
    const clampedWidth = Math.max(25, Math.min(60, newWidth));
    setCommentsWidth(clampedWidth);
  }, [isResizing]);

  const handleMouseUp = useCallback(() => {
    setIsResizing(false);
  }, []);

  React.useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isResizing, handleMouseMove, handleMouseUp]);

  const handleTagUpdate = (updatedRequirement) => {
    setRequirement(prevRequirement => ({
      ...updatedRequirement,
      project: prevRequirement.project // Preserve the populated project data
    }));
  };

  // Comment linking handlers
  const scrollToComment = useCallback((commentId) => {
    if (!commentsContainerRef.current) return;

    const container = commentsContainerRef.current;
    const commentElement = container.querySelector(`[data-comment-id="${commentId}"]`);

    if (commentElement) {
      // Check if comment is visible within the container
      const containerRect = container.getBoundingClientRect();
      const commentRect = commentElement.getBoundingClientRect();

      const isVisible = (
        commentRect.top >= containerRect.top &&
        commentRect.bottom <= containerRect.bottom
      );

      if (!isVisible) {
        setScrollingToComment(commentId);

        // Calculate the scroll position to center the comment in the container
        const containerScrollTop = container.scrollTop;
        const containerHeight = container.clientHeight;
        const commentOffsetTop = commentElement.offsetTop;
        const commentHeight = commentElement.offsetHeight;

        // Calculate target scroll position to center the comment
        const targetScrollTop = commentOffsetTop - (containerHeight / 2) + (commentHeight / 2);

        // Smooth scroll within the container only
        container.scrollTo({
          top: targetScrollTop,
          behavior: 'smooth'
        });

        // Add a temporary highlight effect
        commentElement.style.transition = 'all 0.5s ease';
        commentElement.style.backgroundColor = 'rgba(25, 118, 210, 0.15)';
        commentElement.style.transform = 'scale(1.02)';
        commentElement.style.boxShadow = '0 4px 20px rgba(25, 118, 210, 0.3)';

        setTimeout(() => {
          commentElement.style.backgroundColor = '';
          commentElement.style.transform = '';
          commentElement.style.boxShadow = '';
          setScrollingToComment(null);
        }, 1500);
      }
    }
  }, []);

  const handleLinkHover = useCallback((linkId, isHovering) => {
    setHoveredLinkId(isHovering ? linkId : null);

    if (linkId && isHovering) {
      // Find the linked comment and scroll to it
      const link = linkedTexts.find(l => l.id === linkId);
      if (link) {
        // Small delay to allow hover effects to start
        setTimeout(() => {
          scrollToComment(link.commentId);
        }, 100);
      }
    }
  }, [linkedTexts, scrollToComment]);

  const handleCreateLink = useCallback(async (newLink) => {
    try {
      // Add version to the link
      const linkWithVersion = {
        ...newLink,
        version: requirement.currentVersion
      };

      // Save linked text to database
      await axios.post(`/api/requirements/${requirementId}/linked-texts`, linkWithVersion);

      setLinkedTexts(prev => [...prev, linkWithVersion]);
    } catch (error) {
      console.error('Error creating link:', error);
    }
  }, [requirementId, requirement, axios]);

  const handleCreateNewComment = useCallback(async (selectedText, selectionRange, commentText) => {
    if (!selectedText || !selectionRange || !commentText.trim()) return;

    try {
      // Create the comment first
      const res = await axios.post(`/api/requirements/${requirementId}/comments`, {
        text: commentText,
        version: requirement.currentVersion
      });

      // Update comments state
      setComments(prevComments => {
        const newComments = [...prevComments, res.data];
        return newComments.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
      });

      // Create link to new comment
      const newLink = {
        id: `link-${Date.now()}`,
        text: selectedText,
        commentId: res.data._id,
        startOffset: selectionRange.startOffset,
        endOffset: selectionRange.endOffset,
        highlightColor: res.data.user?.color || '#1976d2',
        version: requirement.currentVersion
      };

      // Save linked text to database
      await axios.post(`/api/requirements/${requirementId}/linked-texts`, newLink);

      setLinkedTexts(prev => [...prev, newLink]);

      // Emit socket event if available
      if (socket) {
        socket.emit('newComment', {
          requirementId,
          comment: res.data
        });
      }
    } catch (error) {
      console.error('Error creating comment:', error);
    }
  }, [requirementId, requirement, axios, socket]);

  useEffect(() => {
    if (authLoading) return;

    if (!isAuthenticated) {
      navigate('/login', { replace: true });
      return;
    }

    const fetchUsers = async () => {
      try {
        const response = await axios.get('/api/users');
        setUsers(response.data);
      } catch (error) {
        console.error('Error fetching users:', error);
      }
    };

    if (requirementId === 'new') {
      setLoading(false);
      fetchUsers();
      return;
    }

    const fetchRequirement = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await axios.get(`/api/requirements/${requirementId}?populate=project,feature`);
        console.log('Fetched requirement:', response.data);

        // If project is just an ID, fetch the full project data
        if (response.data.project && typeof response.data.project === 'string') {
          const projectRes = await axios.get(`/api/projects/${response.data.project}`);
          response.data.project = projectRes.data;
        }

        setRequirement(response.data);

        // Get the current version's labels
        const currentVersion = response.data.versions[response.data.versions.length - 1];
        const currentLabels = currentVersion.labels || [];

        // Fetch all available labels for the dialog
        const labelsRes = await axios.get('/api/labels');
        setAllLabels(labelsRes.data);

        // Handle both populated and non-populated labels
        let fullLabels = [];
        if (currentLabels.length > 0) {
          // Check if labels are already populated (objects) or just IDs
          if (typeof currentLabels[0] === 'object' && currentLabels[0]._id) {
            // Labels are already populated
            fullLabels = currentLabels;
          } else {
            // Labels are just IDs, need to map them to full objects
            const labelMap = labelsRes.data.reduce((map, label) => {
              map[label._id] = label;
              return map;
            }, {});
            fullLabels = currentLabels.map(id => labelMap[id]).filter(Boolean);
          }
        }
        setLabels(fullLabels);

        // Set ALL comments for the main detail screen, ensuring no duplicates
        const allComments = response.data.versions.reduce((acc, version) => {
          return acc.concat(version.comments || []);
        }, []);
        // Create a Map to track unique comments by ID
        const uniqueComments = new Map();
        allComments.forEach(comment => {
          uniqueComments.set(comment._id, comment);
        });
        // Convert back to array and sort by creation date - most recent on top
        setComments(Array.from(uniqueComments.values())
          .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt)));

        // Load linked texts for the current version
        try {
          const linkedTextsResponse = await axios.get(`/api/requirements/${requirementId}/linked-texts?version=${response.data.currentVersion}`);
          setLinkedTexts(linkedTextsResponse.data || []);
        } catch (linkedTextsError) {
          console.error('Error fetching linked texts:', linkedTextsError);
          setLinkedTexts([]);
        }

        // Fetch child requirements if this is a feature
        if (response.data.type === 'feature') {
          console.log('Fetching children for feature:', requirementId);
          const childrenRes = await axios.get(`/api/requirements/${requirementId}/children`);
          console.log('Fetched children:', childrenRes.data);
          setChildRequirements(childrenRes.data);
        } else {
          setChildRequirements([]);
        }

        // If this requirement has a parent feature, fetch it
        let featureId = null;
        if (response.data.feature) {
          featureId = typeof response.data.feature === 'string' ? response.data.feature : response.data.feature._id;
        } else if (response.data.parent) {
          featureId = typeof response.data.parent === 'string' ? response.data.parent : response.data.parent._id;
        }

        if (featureId) {
          try {
            const parentRes = await axios.get(`/api/features/${featureId}?populate=project`);
            setParentFeature(parentRes.data);
          } catch (err) {
            console.error('Error fetching parent feature:', err);
            // Don't set error state here, just log it and continue
            setParentFeature(null);
          }
        } else {
          setParentFeature(null);
        }

        // Fetch users after requirement is loaded
        await fetchUsers();

        // Check if we should open version history (from navigation approval link)
        const searchParams = new URLSearchParams(window.location.search);
        if (searchParams.get('showVersionHistory') === 'true') {
          // Only open version popup if user needs to approve an earlier version they haven't approved yet
          const currentUserId = user?.id || user?._id;
          const approverInfo = response.data.approvers?.find(approver => {
            const approverId = typeof approver.user === 'object' ? approver.user._id : approver.user;
            return approverId === currentUserId;
          });

          if (approverInfo && approverInfo.addedInVersion < response.data.currentVersion) {
            // User was added as approver in an earlier version
            // Check if they have already approved that earlier version
            const hasApprovedEarlierVersion = response.data.approvals?.some(approval => {
              const approvalUserId = typeof approval.user === 'object' ? approval.user._id : approval.user;
              return approvalUserId === currentUserId && approval.version === approverInfo.addedInVersion;
            });

            if (!hasApprovedEarlierVersion) {
              // User hasn't approved the earlier version yet - open version popup
              setShowVersionHistory(true);
              // Set the version where user was added as approver as selected
              const targetVersion = response.data.versions.find(v => v.version === approverInfo.addedInVersion);
              if (targetVersion) {
                const versionComments = response.data.versions
                  .find(v => v.version === targetVersion.version)?.comments || [];

                const filteredComments = versionComments.filter(comment => comment.version === targetVersion.version);

                setSelectedVersion({
                  ...targetVersion,
                  comments: filteredComments.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
                });
                setVersionComment('');
              }
            }
            // If user has already approved earlier version, stay on main screen for current version approval
          }
          // If user needs to approve current version only, just stay on main screen (don't open popup)

          // Clear the query parameter
          navigate(`/requirement/${requirementId}`, { replace: true });
        }
      } catch (err) {
        console.error('Error fetching requirement:', err);
        if (err.response?.status === 401) {
          navigate('/login');
        } else {
          setError(err.response?.data?.message || 'Failed to load requirement');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchRequirement();

    // Socket setup for real-time updates
    if (socket) {
      console.log('Setting up socket connection for requirement:', requirementId);
      console.log('Socket connected:', socket.connected);
      console.log('Socket ID:', socket.id);
      socket.emit('joinRequirement', requirementId);

      socket.on('commentAdded', (data) => {
        console.log('Socket event received - commentAdded:', data);
        if (data.requirementId === requirementId) {
          console.log('Processing comment for current requirement:', data.comment);
          // Update comments state for main detail screen - add all comments
          setComments(prevComments => {
            // Check if comment already exists
            const commentExists = prevComments.some(c => c._id === data.comment._id);
            if (commentExists) {
              console.log('Comment already exists, skipping update');
              return prevComments;
            }
            console.log('Adding new comment to state');
            const newComments = [...prevComments, data.comment];
            return newComments.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
          });

          // Update requirement state to include the new comment in the correct version
          setRequirement(prevReq => {
            if (!prevReq) return prevReq;
            const updatedVersions = prevReq.versions.map(version => {
              if (version.version === data.comment.version) {
                // Check if comment already exists in this version
                const commentExists = (version.comments || []).some(c => c._id === data.comment._id);
                if (commentExists) {
                  return version;
                }
                return {
                  ...version,
                  comments: [...(version.comments || []), data.comment]
                };
              }
              return version;
            });

            return {
              ...prevReq,
              versions: updatedVersions
            };
          });

          // Update selected version in popup if it matches the comment's version
          setSelectedVersion(prevSelected => {
            if (prevSelected && prevSelected.version === data.comment.version) {
              // Check if comment already exists in selected version
              const commentExists = (prevSelected.comments || []).some(c => c._id === data.comment._id);
              if (commentExists) {
                return prevSelected;
              }
              return {
                ...prevSelected,
                comments: [...(prevSelected.comments || []), data.comment]
                  .filter(comment => comment.version === prevSelected.version)
                  .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
              };
            }
            return prevSelected;
          });
        }
      });

      return () => {
        socket.emit('leaveRequirement', requirementId);
        socket.off('commentAdded');
      };
    }
  }, [requirementId, axios, socket, isAuthenticated, navigate, authLoading, user]);

  const getStateColor = (state) => {
    return stateColors[state] || 'default';
  };

  // State transition functions
  const getValidTransitions = async () => {
    try {
      const response = await axios.get(`/api/requirements/${requirementId}/valid-transitions?version=${requirement.currentVersion}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching valid transitions:', error);
      throw error;
    }
  };

  const handleStateTransition = async (newState, direction) => {
    try {
      await axios.post(`/api/requirements/${requirementId}/transition-state`, {
        newState,
        version: requirement.currentVersion
      });

      // Refresh the requirement data
      const response = await axios.get(`/api/requirements/${requirementId}?populate=project,feature`);
      setRequirement(response.data);

      // Trigger navigation update
      window.dispatchEvent(new CustomEvent('updateNavigation', {
        detail: {
          type: 'requirementStateUpdated',
          projectId: requirement.project._id || requirement.project,
          requirementId: requirementId,
          featureId: requirement.feature?._id || requirement.feature,
          newState: newState
        }
      }));
    } catch (error) {
      console.error('Error transitioning state:', error);

      // Show error dialog
      setStateWarningDialog({
        open: true,
        title: 'State Transition Error',
        message: error.response?.data?.msg || 'Failed to transition state',
        onConfirm: () => setStateWarningDialog(prev => ({ ...prev, open: false })),
        showConfirm: false
      });

      throw error;
    }
  };

  const getRequirementIcon = (type) => {
    switch (type) {
      case 'feature':
        return <FolderIcon />;
      case 'user_story':
        return <AssignmentIcon />;
      default:
        return <AssignmentIcon />;
    }
  };

  const handleCommentSubmit = async (e) => {
    if (e && e.preventDefault) e.preventDefault();
    if (!comment.trim() || !requirement) return;

    console.log('Submitting comment:', comment);
    console.log('Requirement ID:', requirementId);
    console.log('Current version:', requirement.currentVersion);

    try {
      const res = await axios.post(`/api/requirements/${requirementId}/comments`, {
        text: comment,
        version: requirement.currentVersion
      });

      console.log('Comment API response:', res.data);

      // Only clear comment after successful API call
      setComment('');

      // For now, always use manual update to ensure comments appear immediately
      console.log('Manually updating comments state');
      setComments(prevComments => {
        const newComments = [...prevComments, res.data];
        return newComments.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
      });

      // Also emit socket event if socket is available for other users
      if (socket) {
        console.log('Emitting socket event for new comment');
        socket.emit('newComment', {
          requirementId,
          comment: res.data
        });
      }
    } catch (error) {
      console.error('Error adding comment:', error);
      console.error('Error details:', error.response?.data);
      // Don't clear the comment on error so user can retry
    }
  };



  const handleVersionClick = (version) => {
    // Get all comments for this version from the requirement's versions
    const versionComments = requirement.versions
      .find(v => v.version === version.version)?.comments || [];

    // Filter comments to only show ones that belong to this version
    const filteredComments = versionComments.filter(comment => comment.version === version.version);

    setSelectedVersion({
      ...version,
      comments: filteredComments.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
    });
    setVersionComment('');
  };

  const handleMenuClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const enterEditMode = () => {
    const currentVersion = requirement.versions[requirement.versions.length - 1];
    setEditedTitle(currentVersion.title);
    setEditedDescription(currentVersion.description);
    setEditMode(true);
  };

  const cancelEdit = () => {
    setEditMode(false);
    setEditedTitle('');
    setEditedDescription('');
  };

  const handleSaveChanges = async () => {
    try {
      if (!requirement) return;

      const currentVersion = requirement.versions[requirement.versions.length - 1];
      if (editedTitle !== currentVersion.title || editedDescription !== currentVersion.description) {
        await axios.put(`/api/requirements/${requirementId}`, {
          title: editedTitle,
          description: editedDescription
          // Don't send state - let server default to 'New' for new versions
        });
        const res = await axios.get(`/api/requirements/${requirementId}?populate=project,feature`);
        setRequirement(res.data);
        setEditMode(false);

        // Trigger navigation update if title changed
        if (editedTitle !== currentVersion.title) {
          window.dispatchEvent(new CustomEvent('updateNavigation', {
            detail: {
              type: 'requirementTitleUpdated',
              projectId: requirement.project._id || requirement.project,
              requirementId: requirementId,
              featureId: requirement.feature?._id || requirement.feature,
              newTitle: editedTitle
            }
          }));
        }
      }
    } catch (error) {
      console.error('Error saving changes:', error);
    }
  };

  const handleCreateElement = async (newElement) => {
    try {
      console.log('Creating new element:', newElement);

      // Create a requirement using the requirements endpoint
      const elementData = {
        project: newElement.project,
        type: newElement.type || 'requirement',
        feature: newElement.parent,
        parent: newElement.parent,
        title: newElement.title,
        description: newElement.description
      };

      console.log('Creating element with data:', elementData);
      const response = await axios.post('/api/requirements', elementData);
      console.log('Created element response:', response.data);

      // After creating the element, fetch it with populated data
      const createdElement = await axios.get(`/api/requirements/${response.data._id}?populate=project,feature`);
      console.log('Fetched created element:', createdElement.data);

      // Navigate to the new requirement
      navigate(`/requirement/${response.data.elementId || response.data._id}`);
    } catch (error) {
      console.error('Error creating element:', error);
      if (error.response) {
        console.error('Error response:', error.response.data);
      }
    }
  };

  const handleVersionCommentSubmit = async () => {
    if (!versionComment.trim() || !selectedVersion) return;

    try {
      const res = await axios.post(`/api/requirements/${requirementId}/comments`, {
        text: versionComment,
        version: selectedVersion.version
      });

      // Remove local state updates since the socket event will handle it
      setVersionComment('');

      socket.emit('newComment', {
        requirementId,
        comment: res.data
      });
    } catch (error) {
      console.error('Error adding comment:', error);
    }
  };

  const handleRemoveLabel = async (labelId) => {
    console.log('Removing label:', labelId);
    try {
      const res = await axios.delete(`/api/requirements/${requirementId}/labels/${labelId}`);
      console.log('Remove label response:', res.data);

      // Get the current version's labels
      const currentVersion = res.data.versions[res.data.versions.length - 1];
      const updatedLabels = currentVersion.labels || [];
      console.log('Updated labels after removal:', updatedLabels);

      // The labels from the server are already full objects
      setLabels(updatedLabels);
      setRequirement(res.data);
    } catch (error) {
      console.error('Error removing label:', error);
    }
  };

  const handleAddLabel = async (labelId) => {
    console.log('Adding label:', labelId);
    try {
      const res = await axios.post(`/api/requirements/${requirementId}/labels`, { labelId });
      console.log('Add label response:', res.data);

      // Get the current version's labels
      const currentVersion = res.data.versions[res.data.versions.length - 1];
      const updatedLabels = currentVersion.labels || [];
      console.log('Updated labels:', updatedLabels);

      // The labels from the server are already full objects
      setLabels(updatedLabels);
      setRequirement(res.data);

      setLabelDialogOpen(false);
    } catch (error) {
      console.error('Error adding label:', error);
    }
  };

  const handleCreateLabel = async () => {
    console.log('Creating new label:', newLabelName);
    try {
      const res = await axios.post('/api/labels', { name: newLabelName });
      console.log('Create label response:', res.data);
      setAllLabels([...allLabels, res.data]);
      await handleAddLabel(res.data._id);
      setNewLabelName('');
    } catch (error) {
      console.error('Error creating label:', error);
    }
  };

  const handleAddTask = async () => {
    try {
      const response = await axios.post(`/api/requirements/${requirementId}/tasks`, {
        text: newTask.text,
        role: newTask.role,
        user: user._id
      });
      setRequirement(response.data);
      setNewTaskDialogOpen(false);
      setNewTask({ text: '', role: '' });

      // Trigger optimistic navigation update
      window.dispatchEvent(new CustomEvent('updateNavigation', {
        detail: {
          type: 'taskAdded',
          projectId: requirement.project._id || requirement.project,
          userId: user._id,
          taskStatus: 'pending'
        }
      }));
    } catch (err) {
      console.error('Error adding task:', err);
    }
  };

  const handleEditTask = (task) => {
    console.log('handleEditTask called with task:', task);
    setEditingTask(task);
    setTaskText(task.text);
    setEditTaskDialogOpen(true);
  };

  const handleUpdateTask = async (taskId) => {
    try {
      const response = await axios.put(`/api/requirements/${requirementId}/tasks/${taskId}`, {
        text: taskText
      });
      setRequirement(response.data);
      setEditTaskDialogOpen(false);
      setEditingTask(null);
      setTaskText('');

      // Trigger optimistic navigation update
      window.dispatchEvent(new CustomEvent('updateNavigation', {
        detail: {
          type: 'taskUpdated',
          projectId: requirement.project._id || requirement.project,
          userId: user._id,
          taskStatus: 'pending' // Task text update doesn't change status
        }
      }));
    } catch (err) {
      console.error('Error updating task:', err);
    }
  };

  const handleDeleteTask = async (taskId) => {
    try {
      const response = await axios.delete(`/api/requirements/${requirementId}/tasks/${taskId}`);
      setRequirement(response.data);

      // Trigger optimistic navigation update
      window.dispatchEvent(new CustomEvent('updateNavigation', {
        detail: {
          type: 'taskDeleted',
          projectId: requirement.project._id || requirement.project,
          userId: user._id,
          taskStatus: 'deleted'
        }
      }));
    } catch (err) {
      console.error('Error deleting task:', err);
    }
  };

  const handleToggleTask = async (taskId) => {
    try {
      // Find the task across ALL versions to determine its current status
      let task = null;

      if (requirement && requirement.versions) {
        for (const version of requirement.versions) {
          if (version.tasks) {
            const foundTask = version.tasks.find(t => t._id === taskId);
            if (foundTask) {
              task = foundTask;
              break;
            }
          }
        }
      }

      if (!task) {
        console.error('Task not found in any version');
        return;
      }

      const newStatus = task.status === 'pending' ? 'completed' : 'pending';

      const response = await axios.put(`/api/requirements/${requirementId}/tasks/${taskId}/toggle`);
      setRequirement(response.data);

      // Trigger optimistic navigation update
      window.dispatchEvent(new CustomEvent('updateNavigation', {
        detail: {
          type: 'taskToggled',
          projectId: requirement.project._id || requirement.project,
          userId: user._id,
          taskStatus: newStatus
        }
      }));
    } catch (err) {
      console.error('Error toggling task:', err);
    }
  };

  const handleAddUser = async (userId, roles = ['developer'], isApprover = false) => {
    try {
      const response = await axios.post(`/api/requirements/${requirementId}/members`, {
        userId,
        roles,
        isApprover
      });
      setRequirement(response.data);

      // Trigger navigation update when user is added
      window.dispatchEvent(new CustomEvent('updateNavigation', {
        detail: {
          type: 'userAdded',
          projectId: requirement.project._id || requirement.project,
          userId: userId,
          requirementId: requirementId
        }
      }));
    } catch (err) {
      console.error('Error adding user:', err);
    }
  };

  const handleToggleApproval = async (userId) => {
    try {
      const response = await axios.post(`/api/requirements/${requirementId}/approvals/toggle`, {
        targetUserId: userId
      });
      setRequirement(response.data);

      // Trigger navigation update when approval status changes with slight delay to ensure server has processed the change
      setTimeout(() => {
        window.dispatchEvent(new CustomEvent('updateNavigation', {
          detail: {
            type: 'approvalToggled',
            projectId: requirement.project._id || requirement.project,
            userId: user._id,
            requirementId: requirementId
          }
        }));
      }, 100);
    } catch (err) {
      console.error('Error toggling approval:', err);
    }
  };

  const handleAddUserWithTask = async () => {
    try {
      // First, add the user as a member if they're not already
      const isAlreadyMember = requirement.members.some(
        member => member.user._id === selectedUser
      );

      if (!isAlreadyMember) {
        // Add user with selected role (if any)
        const roles = selectedRole ? [selectedRole] : [];
        const response = await axios.post(`/api/requirements/${requirementId}/members`, {
          userId: selectedUser,
          roles: roles,
          isApprover: isApprover
        });
        setRequirement(response.data);
      } else if (selectedRole) {
        // User is already a member, but we might need to add a new role
        const existingMember = requirement.members.find(
          member => member.user._id === selectedUser
        );
        if (existingMember && !existingMember.roles.includes(selectedRole)) {
          // Add the new role to existing member
          const updatedRoles = [...existingMember.roles, selectedRole];
          await axios.put(`/api/requirements/${requirementId}/members/${selectedUser}`, {
            roles: updatedRoles
          });
        }
      }

      // If there's a task, add it
      if (taskText.trim()) {
        // Determine the role to use for the task
        let taskRole = selectedRole; // Use explicitly selected role first

        if (!taskRole) {
          // No role selected - check if user already has roles
          const existingMember = requirement.members.find(
            member => member.user._id === selectedUser
          );

          if (existingMember && existingMember.roles.length > 0) {
            // User has existing roles - use the first one as default
            taskRole = existingMember.roles[0];
          }
          // If user has no existing roles and no role selected, taskRole remains undefined
        }

        const taskData = {
          text: taskText,
          user: selectedUser
        };

        // Only include role if we have one
        if (taskRole) {
          taskData.role = taskRole;
        }

        const response = await axios.post(`/api/requirements/${requirementId}/tasks`, taskData);
        setRequirement(response.data);

        // Trigger optimistic navigation update when task is added
        window.dispatchEvent(new CustomEvent('updateNavigation', {
          detail: {
            type: 'taskAdded',
            projectId: requirement.project._id || requirement.project,
            userId: selectedUser,
            taskStatus: 'pending'
          }
        }));
      } else {
        // If no task, just refresh the requirement to get updated members
        const response = await axios.get(`/api/requirements/${requirementId}?populate=project,feature`);
        setRequirement(response.data);

        // Trigger navigation update when user is added (even without task)
        window.dispatchEvent(new CustomEvent('updateNavigation', {
          detail: {
            type: 'userAdded',
            projectId: requirement.project._id || requirement.project,
            userId: selectedUser,
            requirementId: requirementId
          }
        }));
      }

      // Close dialog and reset state
      setTaskDialogOpen(false);
      setSelectedUser('');
      setTaskText('');
      setSelectedRole('');
      setIsApprover(true); // Reset to default
    } catch (err) {
      console.error('Error adding user with task:', err);
    }
  };

  const handleRemoveUser = async (userId) => {
    try {
      const response = await axios.delete(`/api/requirements/${requirementId}/members/${userId}`);
      setRequirement(response.data);
    } catch (err) {
      console.error('Error removing user:', err);
    }
  };

  const renderTaskBubble = (task, version) => {
    // Check if task.user is already populated (object) or just an ID (string)
    const user = typeof task.user === 'object' && task.user !== null
      ? task.user
      : users.find(u => u._id === task.user);

    const isHistoricalTask = version !== requirement.currentVersion;

    // Create label with version and task text
    const versionPrefix = isHistoricalTask ? `v${version} ` : '';
    const taskText = task.text;
    const fullLabel = `${versionPrefix}${taskText}`;
    const truncatedLabel = fullLabel.length > 25 ? `${fullLabel.substring(0, 25)}...` : fullLabel;

    return (
      <Tooltip title={`${task.text} (Version ${version})`} key={`task-${task._id}`}>
        <Chip
          label={truncatedLabel}
          avatar={<EnhancedAvatar user={user} size={24} sx={{ color: '#FFFFFF !important' }} />}
          sx={{
            bgcolor: user?.color + '20',
            m: 0.5,
            '&:hover': { cursor: 'pointer' },
            opacity: isHistoricalTask ? 0.8 : 1,
            // Removed dotted border for historical tasks
            '& .MuiChip-avatar': {
              color: '#FFFFFF !important'
            }
          }}
          onClick={() => handleEditTask(task)}
          onDelete={() => {
            if (task._id) {
              handleToggleTask(task._id);
            }
          }}
          deleteIcon={task.status === 'pending' ? <DoneIcon /> : undefined}
        />
      </Tooltip>
    );
  };

  const handleToggleUserManagement = () => {
    setUserManagementExpanded(!userManagementExpanded);
  };

  const renderTaskSection = () => {
    // Get tasks from ALL versions, not just the latest
    const allTasks = [];
    if (requirement && requirement.versions) {
      requirement.versions.forEach(version => {
        if (version.tasks) {
          version.tasks.forEach(task => {
            allTasks.push({ ...task, version: version.version });
          });
        }
      });
    }

    const pendingTasks = allTasks.filter(t => t.status === 'pending');
    const completedTasks = allTasks.filter(t => t.status === 'completed');

    const elementType = requirement?.type === 'feature' ? 'Feature' :
                       requirement?.type === 'user_story' ? 'User Story' :
                       'Requirement';

    // Calculate summary counts for collapsed state
    const totalMembers = requirement?.members?.length || 0;
    const totalActiveTasks = pendingTasks.length;
    const totalCompletedTasks = completedTasks.length;

    return (
      <Paper sx={{
        p: 2,
        mb: 3,
        border: '1px solid',
        borderColor: 'divider',
        boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)'
      }}>
        {/* Collapsible Header */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: userManagementExpanded ? 2 : 0,
            cursor: 'pointer',
            py: 1,
            '&:hover': {
              bgcolor: 'action.hover',
              borderRadius: 1
            }
          }}
          onClick={handleToggleUserManagement}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <IconButton size="small" sx={{ p: 0 }}>
              {userManagementExpanded ? <ExpandMoreIcon /> : <ChevronRightIcon />}
            </IconButton>
            <GroupIcon sx={{ color: 'primary.main' }} />
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              User Management
            </Typography>
            {!userManagementExpanded && (
              <Box sx={{ display: 'flex', gap: 1, ml: 2 }}>
                <Chip
                  label={`${totalMembers} member${totalMembers !== 1 ? 's' : ''}`}
                  size="small"
                  variant="outlined"
                  color="primary"
                />
                <Chip
                  label={`${totalActiveTasks} active task${totalActiveTasks !== 1 ? 's' : ''}`}
                  size="small"
                  variant="outlined"
                  color="warning"
                />
                <Chip
                  label={`${totalCompletedTasks} completed`}
                  size="small"
                  variant="outlined"
                  color="success"
                />
              </Box>
            )}
          </Box>
          {userManagementExpanded && (
            <Chip
              label="Add User"
              icon={<AddIcon />}
              onClick={(e) => {
                e.stopPropagation();
                setTaskDialogOpen(true);
              }}
              variant="outlined"
              color="primary"
              size="small"
              sx={{
                '&:hover': {
                  borderColor: 'primary.main',
                  backgroundColor: 'rgba(25, 118, 210, 0.1)',
                  boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)'
                }
              }}
            />
          )}
        </Box>

        {/* Collapsible Content */}
        <Collapse in={userManagementExpanded} timeout="auto" unmountOnExit>
          <Grid container spacing={2}>
            <Grid item xs={4}>
              <Typography variant="subtitle1" gutterBottom sx={{ textAlign: 'center' }}>
                Members of this {elementType}
              </Typography>
              <Stack direction="row" flexWrap="wrap" spacing={1}>
                {requirement?.members?.map(member => {
                  // Handle both populated and non-populated user fields
                  const user = member.user;
                  if (!user) return null;

                  // Handle case where user might be just an ID string
                  const userId = typeof user === 'object' ? user._id : user;
                  const username = typeof user === 'object' ? user.username : 'Unknown User';
                  const userColor = typeof user === 'object' ? user.color : '#1976d2';

                  // Check if user is an approver
                  const isApprover = requirement?.approvers?.some(
                    approver => (typeof approver.user === 'object' ? approver.user._id : approver.user) === userId
                  ) || false;

                  // Check if user has approved current version
                  const isApproved = requirement?.approvals?.some(
                    approval => (typeof approval.user === 'object' ? approval.user._id : approval.user) === userId &&
                               approval.version === requirement.currentVersion
                  ) || false;

                  // Check if current user can toggle approval
                  const currentUserId = user?._id; // Current logged-in user
                  const canToggleApproval = (
                    (isApprover && userId === currentUserId) || // User can approve for themselves if they're an approver
                    requirement?.project?.createdBy === currentUserId // Project creator can approve for anyone
                  );

                  return (
                    <ApprovalBubble
                      key={`member-${userId}`}
                      user={user}
                      isApprover={isApprover}
                      isApproved={isApproved}
                      canToggleApproval={canToggleApproval}
                      onToggleApproval={() => handleToggleApproval(userId)}
                      roles={member.roles}
                    />
                  );
                }).filter(Boolean) || []}
              </Stack>
            </Grid>

            <Grid item xs={4}>
              <Typography variant="subtitle1" gutterBottom sx={{ textAlign: 'center' }}>
                Active Tasks
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                {pendingTasks.map(task => renderTaskBubble(task, task.version))}
              </Box>
            </Grid>

            <Grid item xs={4}>
              <Typography variant="subtitle1" gutterBottom sx={{ textAlign: 'center' }}>
                Completed Tasks
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                {completedTasks.map(task => renderTaskBubble(task, task.version))}
              </Box>
            </Grid>
          </Grid>
        </Collapse>
      </Paper>
    );
  };

  if (authLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography color="error">Error: {error}</Typography>
        <Button onClick={() => navigate('/')} sx={{ mt: 2 }}>
          Back to Projects
        </Button>
      </Box>
    );
  }

  if (requirementId === 'new') {
    // Get feature and project IDs from URL
    const searchParams = new URLSearchParams(window.location.search);
    const featureId = searchParams.get('feature');
    const projectId = searchParams.get('project');

    return (
      <Box sx={{ p: 3 }}>
        <Breadcrumbs
          items={[
            {
              label: 'Projects',
              path: '/',
              icon: FolderIcon
            },
            {
              label: 'Project',
              path: `/project/${projectId}`,
              icon: FolderIcon
            },
            {
              label: 'New Requirement',
              path: `/requirement/new?feature=${featureId}&project=${projectId}`,
              icon: AssignmentIcon
            }
          ]}
        />
        <Typography variant="h4" gutterBottom>Create New Requirement</Typography>
        <Paper sx={{ p: 3, mt: 2 }}>
          <Box component="form" onSubmit={async (e) => {
            e.preventDefault();
            try {
              const response = await axios.post('/api/requirements', {
                project: projectId,
                type: 'requirement',
                feature: featureId,
                parent: featureId,
                title: editedTitle,
                description: editedDescription
              });
              navigate(`/requirement/${response.data.elementId || response.data._id}`);
            } catch (error) {
              console.error('Error creating requirement:', error);
            }
          }}>
            <TextField
              fullWidth
              label="Title"
              value={editedTitle}
              onChange={(e) => setEditedTitle(e.target.value)}
              margin="normal"
              required
            />
            <TextField
              fullWidth
              label="Description"
              value={editedDescription}
              onChange={(e) => setEditedDescription(e.target.value)}
              margin="normal"
              multiline
              rows={4}
              required
            />
            <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
              <Button
                variant="contained"
                type="submit"
                disabled={!editedTitle || !editedDescription}
              >
                Create Requirement
              </Button>
              <Button
                variant="outlined"
                onClick={() => navigate(-1)}
              >
                Cancel
              </Button>
            </Box>
          </Box>
        </Paper>
      </Box>
    );
  }

  if (!requirement) {
    return null;
  }

  const currentVersion = requirement.versions[requirement.versions.length - 1];

  return (
    <MainContainer>
      <Breadcrumbs
        items={[
          {
            label: 'Projects',
            path: '/',
            icon: FolderIcon
          },
          ...(requirement?.project ? [{
            label: requirement.project.name || 'Project',
            path: `/project/${requirement.project.elementId || requirement.project._id}`,
            icon: FolderIcon
          }] : []),
          ...(parentFeature ? [{
            label: parentFeature.versions[parentFeature.versions.length - 1].title,
            path: `/feature/${parentFeature.elementId || parentFeature._id}`,
            icon: FolderIcon
          }] : []),
          {
            label: currentVersion?.title || 'New Requirement',
            path: `/requirement/${requirementId}`,
            icon: getRequirementIcon(requirement?.type)
          }
        ]}
      />
      <Box>
        <HeaderRow>
          <HeaderLeft>
            {editMode ? (
              <TextField
                variant="outlined"
                value={editedTitle}
                onChange={(e) => setEditedTitle(e.target.value)}
                sx={titleInputStyles}
              />
            ) : (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                <ElementIdDisplay elementId={requirement.elementId} />
                <Typography variant="h4">
                  {currentVersion.title}
                  <Chip
                    label={`v${requirement.currentVersion}`}
                    size="small"
                    icon={<HistoryIcon />}
                    onClick={() => {
                      setShowVersionHistory(true);
                      handleVersionClick(currentVersion);
                    }}
                    sx={{ ml: 1, mr: 3 }}
                  />
                </Typography>
              </Box>
            )}
            <StateTransitionControl
              currentState={currentVersion.state}
              onTransition={handleStateTransition}
              getValidTransitions={getValidTransitions}
              disabled={editMode}
              size="small"
            />
          </HeaderLeft>
          {/* 3-dots menu moved up here */}
          <Box>
            {editMode ? (
              <Box sx={layoutStyles.editButtonGroup}>
                <Button
                  variant="contained"
                  startIcon={<SaveIcon />}
                  onClick={handleSaveChanges}
                >
                  Save
                </Button>
                <Button
                  variant="outlined"
                  onClick={cancelEdit}
                >
                  Cancel
                </Button>
              </Box>
            ) : (
              <>
                {requirement?.type === 'feature' && (
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => {
                      setNewElement({
                        type: 'requirement',
                        title: '',
                        description: '',
                        parent: requirement._id
                      });
                      setNewElementDialogOpen(true);
                    }}
                    sx={{ mr: 1 }}
                  >
                    Add New Element
                  </Button>
                )}
                <IconButton onClick={handleMenuClick}>
                  <MoreVertIcon />
                </IconButton>
                <Menu
                  anchorEl={anchorEl}
                  open={Boolean(anchorEl)}
                  onClose={handleMenuClose}
                >
                  <MenuItem onClick={() => {
                    enterEditMode();
                    handleMenuClose();
                  }}>
                    <EditIcon sx={{ mr: 1 }} />
                    Edit
                  </MenuItem>
                </Menu>
              </>
            )}
          </Box>
        </HeaderRow>
        {/* Labels row */}
        <LabelRowContainer>
          {/* Add Label button - fixed position on the left */}
          <Chip
            label="Add Label"
            icon={<AddIcon />}
            onClick={() => setLabelDialogOpen(true)}
            variant="outlined"
            color="primary"
            size="small"
            sx={{
              flexShrink: 0,
              '&:hover': {
                borderColor: 'primary.main',
                backgroundColor: 'rgba(25, 118, 210, 0.1)',
                boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)'
              }
            }}
          />
          {/* Labels appear to the right of the Add button */}
          <LabelList labels={labels} onDelete={handleRemoveLabel} />
        </LabelRowContainer>
      </Box>

      {/* Split View Layout: Description and Comments */}
      <Box ref={containerRef} sx={{ display: 'flex', gap: 3, position: 'relative', mb: 3 }}>
        {/* Left: Description */}
        <Paper sx={{
          flex: 1,
          width: `${100 - commentsWidth}%`,
          p: 3,
          borderRadius: 3,
          boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
          border: '1px solid',
          borderColor: 'divider',
          transition: isResizing ? 'none' : 'width 0.2s ease'
        }}>
          <Typography variant="h6" sx={{ mb: 2 }}>Description</Typography>
          {editMode ? (
            <RichTextEditor
              value={editedDescription}
              onChange={setEditedDescription}
              placeholder="Enter description..."
              height={200}
              disabled={false}
            />
          ) : (
            <RichTextDisplayWithLinking
              content={currentVersion.description}
              comments={comments}
              linkedTexts={linkedTexts}
              onCreateLink={handleCreateLink}
              onCreateNewComment={handleCreateNewComment}
              onLinkHover={handleLinkHover}
              hoveredLinkId={hoveredLinkId}
            />
          )}
        </Paper>

        {/* Resize Handle */}
        <Box
          onMouseDown={handleMouseDown}
          sx={{
            width: '4px',
            cursor: 'col-resize',
            backgroundColor: isResizing ? 'primary.main' : 'divider',
            '&:hover': {
              backgroundColor: 'primary.main'
            },
            transition: 'background-color 0.2s ease',
            borderRadius: 1
          }}
        />

        {/* Right: Comments */}
        <Paper sx={{
          width: `${commentsWidth}%`,
          minWidth: '300px',
          maxHeight: '80vh',
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
          p: 3,
          borderRadius: 3,
          transition: isResizing ? 'none' : 'width 0.2s ease'
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
              <ChatIcon sx={{ mr: 1, color: '#1976d2' }} />
              Comments
            </Typography>
            <Badge badgeContent={comments.length} color="primary" />
            {scrollingToComment && (
              <Chip
                size="small"
                label="Auto-scrolling..."
                color="primary"
                variant="outlined"
                sx={{
                  '@keyframes pulse': {
                    '0%': { opacity: 1 },
                    '50%': { opacity: 0.5 },
                    '100%': { opacity: 1 },
                  },
                  animation: 'pulse 1s infinite',
                  fontSize: '0.7rem',
                  height: 20
                }}
              />
            )}
          </Box>
          <Box
            ref={commentsContainerRef}
            sx={{ flex: 1, overflow: 'auto', mb: 2 }}
          >
            <List>
              {comments.map((comment) => {
                const hasLinkedText = linkedTexts.some(link => link.commentId === comment._id);
                const isHovered = hoveredLinkId && linkedTexts.some(link => link.commentId === comment._id && link.id === hoveredLinkId);

                return (
                <ListItem
                  key={comment._id}
                  data-comment-id={comment._id}
                  alignItems="flex-start"
                  sx={{
                    transition: 'all 0.2s ease',
                    borderRadius: 1,
                    mb: 1,
                    ...(hasLinkedText && {
                      border: `2px solid ${comment.user?.color || '#1976d2'}40`,
                      backgroundColor: `${comment.user?.color || '#1976d2'}08`,
                    }),
                    ...(isHovered && {
                      backgroundColor: `${comment.user?.color || '#1976d2'}20`,
                      transform: 'scale(1.02)',
                      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                      border: `2px solid ${comment.user?.color || '#1976d2'}80`,
                    }),
                  }}
                >
                  <ListItemAvatar>
                    <EnhancedAvatar user={comment.user} size={40} />
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <React.Fragment>
                        <Typography
                          component="span"
                          variant="subtitle2"
                          color="text.primary"
                        >
                          {comment.user?.username || 'Unknown'}
                          <Typography
                            component="span"
                            variant="caption"
                            color="text.secondary"
                            sx={{ ml: 1 }}
                          >
                            (v{comment.version})
                          </Typography>
                        </Typography>
                      </React.Fragment>
                    }
                    secondary={
                      <React.Fragment>
                        <Typography
                          component="span"
                          variant="body2"
                          color="text.primary"
                        >
                          {comment.text}
                        </Typography>
                        <Typography
                          component="p"
                          variant="caption"
                          color="text.secondary"
                        >
                          {new Date(comment.createdAt).toLocaleString()}
                        </Typography>
                      </React.Fragment>
                    }
                  />
                </ListItem>
                );
              })}
            </List>
          </Box>
          <Box sx={{ mt: 2 }}>
            <TextField
              fullWidth
              multiline
              rows={3}
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="Add a comment..."
              variant="outlined"
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  '&:hover fieldset': {
                    borderColor: 'primary.main',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: 'primary.main',
                  }
                }
              }}
            />
            <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                variant="contained"
                color="primary"
                startIcon={<AddIcon />}
                onClick={handleCommentSubmit}
                disabled={!comment.trim()}
                sx={{
                  borderRadius: 2,
                  textTransform: 'none',
                  fontWeight: 600,
                  mt: 1,
                  px: 3,
                  py: 1
                }}
              >
                Add Comment
              </Button>
            </Box>
          </Box>
        </Paper>
      </Box>

      {/* Main Content Section */}
      <Box sx={{ display: 'flex', gap: 3 }}>
        {/* Left: Requirements and other content */}
        <Box sx={{ flex: 1 }}>
          {requirement?.type === 'feature' && childRequirements.length > 0 && (
            <Paper sx={paperStyles.standard}>
              <Typography variant="h6" gutterBottom sx={typographyStyles.sectionHeader}>Child Requirements</Typography>
              <List>
                {childRequirements.map(child => {
                  const childVersion = child.versions[child.versions.length - 1];
                  return (
                    <ListItem
                      key={child._id}
                      button
                      onClick={() => navigate(`/requirement/${child.elementId || child._id}`)}
                    >
                      <ListItemIcon>
                        {getRequirementIcon(child.type)}
                      </ListItemIcon>
                      <ListItemText
                        primary={childVersion.title}
                        secondary={
                          <RichTextDisplay
                            content={childVersion.description}
                            inline={true}
                            sx={{
                              fontSize: '0.875rem',
                              color: 'text.secondary'
                            }}
                          />
                        }
                      />
                      <Chip
                        label={childVersion.state}
                        color={getStateColor(childVersion.state)}
                        size="small"
                        sx={{ ml: 1 }}
                      />
                    </ListItem>
                  );
                })}
              </List>
            </Paper>
          )}

          {/* Release Tags Section */}
          <Box sx={{ mb: 3 }}>
            <TagManager
              item={requirement}
              itemType="requirement"
              onUpdate={handleTagUpdate}
            />
          </Box>

          {/* Document History Section */}
          <DocumentLabels
            element={requirement}
            elementType="requirement"
            projectId={requirement.project._id || requirement.project}
            isCollapsible={true}
          />

          {renderTaskSection()}

          <Box sx={{ mb: 3 }}>
            <TestManagement requirement={requirement} />
          </Box>
        </Box>
      </Box>

      <VersionHistoryDialog
        open={showVersionHistory}
        onClose={() => {
          setShowVersionHistory(false);
          setSelectedVersion(null);
          setVersionComment('');
        }}
        requirement={requirement}
        selectedVersion={selectedVersion}
        onVersionClick={handleVersionClick}
        versionComment={versionComment}
        setVersionComment={setVersionComment}
        onVersionCommentSubmit={handleVersionCommentSubmit}
      />

      <Dialog
        open={newElementDialogOpen}
        onClose={() => setNewElementDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Add New Element to Feature</DialogTitle>
        <DialogContent>
          <TextField
            select
            label="Type"
            value={newElement.type}
            onChange={(e) => {
              const type = e.target.value;
              setNewElement({
                ...newElement,
                type,
                // Ensure parent is set to current feature for non-feature types
                parent: type === 'feature' ? null : requirement._id
              });
            }}
            fullWidth
            margin="normal"
            disabled={requirement.type !== 'feature'}
            helperText={requirement.type !== 'feature' ? 'Only Features can have child elements' : ''}
          >
            <MenuItem value="user_story">User Story</MenuItem>
            <MenuItem value="requirement">Requirement</MenuItem>
          </TextField>
          <TextField
            label="Title"
            value={newElement.title}
            onChange={(e) => setNewElement({ ...newElement, title: e.target.value })}
            fullWidth
            margin="normal"
            required
          />
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" sx={{ mb: 1 }}>Description *</Typography>
            <RichTextEditor
              value={newElement.description}
              onChange={(value) => setNewElement({ ...newElement, description: value })}
              placeholder="Enter description..."
              height={150}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setNewElementDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={() => handleCreateElement(newElement)}
            variant="contained"
            disabled={!newElement.title || !newElement.description || (requirement.type !== 'feature' && newElement.type !== 'feature')}
          >
            Create
          </Button>
        </DialogActions>
      </Dialog>

      <LabelDialog
        open={labelDialogOpen}
        onClose={() => {
          setLabelDialogOpen(false);
          setNewLabelName('');
        }}
        allLabels={allLabels}
        labels={labels}
        onAddLabel={handleAddLabel}
        newLabelName={newLabelName}
        setNewLabelName={setNewLabelName}
        onCreateLabel={handleCreateLabel}
      />

      <TaskDialog
        open={taskDialogOpen}
        onClose={() => setTaskDialogOpen(false)}
        users={users}
        selectedUser={selectedUser}
        setSelectedUser={setSelectedUser}
        taskText={taskText}
        setTaskText={setTaskText}
        selectedRole={selectedRole}
        setSelectedRole={setSelectedRole}
        isApprover={isApprover}
        setIsApprover={setIsApprover}
        onAdd={handleAddUserWithTask}
      />

      <Dialog
        open={editTaskDialogOpen}
        onClose={() => {
          console.log('Edit dialog closing, current editingTask:', editingTask);
          setEditTaskDialogOpen(false);
          setEditingTask(null);
          setTaskText('');
        }}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Edit Task</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            {editingTask && editingTask.version && (
              <Box sx={{ mb: 2, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  <HistoryIcon fontSize="small" sx={{ mr: 1, verticalAlign: 'middle' }} />
                  This task is from version {editingTask.version}
                  {editingTask.version !== requirement.currentVersion && (
                    <Typography component="span" variant="caption" sx={{ ml: 1, fontStyle: 'italic' }}>
                      (Historical task - version cannot be changed)
                    </Typography>
                  )}
                </Typography>
              </Box>
            )}
            <TextField
              label="Task Description"
              value={taskText}
              onChange={(e) => setTaskText(e.target.value)}
              fullWidth
              multiline
              rows={3}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => {
            console.log('Cancel clicked, current editingTask:', editingTask);
            setEditTaskDialogOpen(false);
            setEditingTask(null);
            setTaskText('');
          }}>
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={() => {
              console.log('Save clicked, current editingTask:', editingTask);
              //console.log('taskText: ', taskText);
              if (editingTask && editingTask._id) {
                handleUpdateTask(editingTask._id);
              } else {
                console.error('No editingTask or editingTask._id found');
              }
            }}
            disabled={!taskText.trim() || !editingTask}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>

      <AddUserDialog
        open={addUserDialogOpen}
        onClose={() => setAddUserDialogOpen(false)}
        onAdd={handleAddUser}
        existingUsers={requirement?.members || []}
        title="Add User to Requirement"
        showApproverOption={true}
      />

      <Dialog
        open={newTaskDialogOpen}
        onClose={() => setNewTaskDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Add New Task</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
            <TextField
              label="Task Description"
              value={newTask.text}
              onChange={(e) => setNewTask({ ...newTask, text: e.target.value })}
              fullWidth
              multiline
              rows={3}
            />
            <FormControl fullWidth>
              <InputLabel>Role</InputLabel>
              <Select
                value={newTask.role}
                label="Role"
                onChange={(e) => setNewTask({ ...newTask, role: e.target.value })}
                defaultValue={user?.role || 'developer'}
              >
                {ROLES.map((role) => (
                  <MenuItem key={role} value={role}>
                    {role.split('_').map(word =>
                      word.charAt(0).toUpperCase() + word.slice(1)
                    ).join(' ')}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setNewTaskDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleAddTask}
            variant="contained"
            disabled={!newTask.text || !newTask.role}
          >
            Add
          </Button>
        </DialogActions>
      </Dialog>

      {/* State Warning Dialog */}
      <StateWarningDialog
        open={stateWarningDialog.open}
        onClose={() => setStateWarningDialog(prev => ({ ...prev, open: false }))}
        onConfirm={stateWarningDialog.onConfirm}
        title={stateWarningDialog.title}
        message={stateWarningDialog.message}
        showConfirm={stateWarningDialog.showConfirm !== false}
      />
    </MainContainer>
  );
};

export default RequirementView;