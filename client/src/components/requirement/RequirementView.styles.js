// Styles for RequirementView component
// Extracted from inline sx props for better maintainability

// State color mappings - logical workflow progression
export const stateColors = {
  // Requirement states
  'New': 'default',                    // Gray - Initial state
  'Being Drafted': 'info',             // Blue - Documentation phase
  'Requiring Approval': 'warning',     // Orange - Needs attention/review
  'Ready for Dev': 'success',          // Green - Approved, ready to start
  'Being Built': 'secondary',          // Purple - Development in progress
  'Ready for Test': 'warning',         // Amber - Ready for validation
  'Under Test': 'error',               // Red - Critical testing phase
  'Testing Satisfactory': 'success',   // Green - Tests passed
  'Shipped/Deployed to Customer': 'success', // Green - Final success
  // Feature states
  'NEW': 'default',                    // Gray - Initial state
  'OPEN': 'info',                      // Blue - Active work
  'CLOSED': 'success'                  // Green - Completed
};

// Common Paper styles used throughout the component
export const paperStyles = {
  standard: {
    p: 3,
    mb: 3,
    borderRadius: 3,
    boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
    border: '1px solid',
    borderColor: 'divider'
  },
  userManagement: {
    p: 2,
    mb: 3
  }
};

// Layout styles
export const layoutStyles = {
  mainContainer: {
    p: 3,
    bgcolor: 'background.default',
    minHeight: '100vh'
  },
  headerRow: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    mb: 2
  },
  headerLeft: {
    display: 'flex',
    alignItems: 'center',
    gap: 2
  },
  editButtonGroup: {
    mt: 2,
    display: 'flex',
    gap: 1
  }
};

// Collapsible section styles
export const collapsibleStyles = {
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    cursor: 'pointer',
    py: 1,
    '&:hover': {
      bgcolor: 'action.hover',
      borderRadius: 1
    }
  },
  headerExpanded: {
    mb: 2
  },
  headerCollapsed: {
    mb: 0
  },
  summaryChips: {
    display: 'flex',
    gap: 1,
    ml: 2
  }
};

// Complex neumorphic button styles for Add User/Add Label buttons
export const neumorphicButtonStyles = {
  base: {
    position: 'relative',
    width: 50,
    height: 50,
    borderRadius: '50%',
    bgcolor: 'background.paper',
    border: '2px solid',
    borderColor: 'primary.light',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    transition: 'all 0.3s ease-in-out',
    overflow: 'hidden',
    // Neumorphism effect
    boxShadow: 'inset 3px 3px 6px rgba(0,0,0,0.1), inset -3px -3px 6px rgba(255,255,255,0.8), 0 2px 4px rgba(0,0,0,0.1)',
    '&:hover': {
      transform: 'scale(1.08) translateY(-2px)',
      boxShadow: `
        inset 1px 1px 3px rgba(0,0,0,0.2),
        inset -1px -1px 3px rgba(255,255,255,1),
        0 8px 16px rgba(0,0,0,0.2),
        0 0 20px rgba(25,118,210,0.3)
      `,
      borderColor: 'primary.main',
      '&::before': {
        opacity: 1
      }
    },
    '&:active': {
      transform: 'scale(0.95)',
      boxShadow: 'inset 4px 4px 8px rgba(0,0,0,0.2), inset -4px -4px 8px rgba(255,255,255,0.7)'
    },
    '&::before': {
      content: '""',
      position: 'absolute',
      top: '10%',
      left: '15%',
      width: '30%',
      height: '50%',
      background: 'linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0.4) 50%, transparent 100%)',
      borderRadius: '50%',
      transform: 'rotate(-45deg)',
      zIndex: 3,
      opacity: 0,
      transition: 'opacity 0.3s ease-in-out'
    }
  },
  backgroundIcon: {
    position: 'absolute',
    color: 'primary.light',
    opacity: 0.2,
    fontSize: '2rem',
    zIndex: 1
  },
  textContainer: {
    position: 'absolute',
    top: 8,
    left: '50%',
    transform: 'translateX(-50%)',
    zIndex: 2
  }
};

// Title input styles for edit mode
export const titleInputStyles = {
  '& .MuiOutlinedInput-root': {
    fontSize: '2.125rem',
    fontWeight: 400,
    lineHeight: 1.235
  }
};

// Comment styles
export const commentStyles = {
  timestamp: {
    ml: 1
  },
  form: {
    mt: 2,
    display: 'flex',
    gap: 1
  }
};

// Typography styles
export const typographyStyles = {
  sectionHeader: {
    fontWeight: 600
  }
};

// User Management styles
export const userManagementStyles = {
  container: {
    p: 2,
    mb: 3
  },
  addUserButton: {
    ...neumorphicButtonStyles.base,
    flexShrink: 0
  }
};

// Task section styles
export const taskStyles = {
  container: {
    mb: 3
  },
  taskChip: {
    mr: 1,
    mb: 1
  }
};

// Label row styles
export const labelRowStyles = {
  container: {
    display: 'flex',
    alignItems: 'center',
    gap: 1,
    flexWrap: 'wrap',
    mb: 3
  }
};

// Version history styles
export const versionHistoryStyles = {
  detailsContainer: {
    mt: 2
  },
  changeItem: {
    ml: 2,
    mb: 1
  },
  approversSection: {
    mt: 2
  },
  commentsSection: {
    mt: 2
  },
  commentBox: {
    mb: 2,
    p: 2,
    bgcolor: 'background.paper',
    borderRadius: 1
  }
};
