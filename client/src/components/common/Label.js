import React from 'react';
import { Chip, Box } from '@mui/material';

const Label = ({ label, onDelete }) => {
  return (
    <Chip
      label={label.name}
      variant="outlined"
      size="small"
      sx={{
        borderColor: label.color,
        color: label.color,
        backgroundColor: 'transparent',
        flexShrink: 0,
        '&:hover': {
          borderColor: label.color,
          backgroundColor: `${label.color}20`, // 20% opacity of the label color
          boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)'
        },
        '& .MuiChip-deleteIcon': {
          color: label.color,
          '&:hover': {
            color: label.color,
            backgroundColor: `${label.color}30` // Slightly more opacity on hover
          }
        }
      }}
      onDelete={onDelete}
    />
  );
};

const LabelList = ({ labels, onDelete }) => {
  // Safety check for labels
  if (!labels || !Array.isArray(labels)) {
    return null;
  }

  return (
    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
      {labels.filter(label => label && label._id && label.name).map((label) => (
        <Label
          key={label._id}
          label={label}
          onDelete={onDelete ? () => onDelete(label._id) : undefined}
        />
      ))}
    </Box>
  );
};

export default LabelList;