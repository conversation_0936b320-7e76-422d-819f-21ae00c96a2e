import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Chip,
  Tooltip,
  CircularProgress
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  ArrowForward as ArrowForwardIcon
} from '@mui/icons-material';

const StateTransitionControl = ({
  currentState,
  onTransition,
  getValidTransitions,
  disabled = false,
  showStateChip = true,
  size = 'medium'
}) => {
  const [transitions, setTransitions] = useState(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchTransitions = async () => {
      if (getValidTransitions) {
        try {
          const result = await getValidTransitions();
          setTransitions(result);
        } catch (error) {
          console.error('Error fetching valid transitions:', error);
        }
      }
    };

    fetchTransitions();
  }, [currentState, getValidTransitions]);

  const handleTransition = async (newState, direction) => {
    if (disabled || loading) return;

    setLoading(true);
    try {
      await onTransition(newState, direction);
      // Refresh transitions after successful transition
      if (getValidTransitions) {
        const result = await getValidTransitions();
        setTransitions(result);
      }
    } catch (error) {
      console.error('Error during state transition:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStateColor = (state) => {
    const colors = {
      // Requirement states - logical workflow progression
      'New': '#6B7280',                           // Gray - Initial state
      'Being Drafted': '#3B82F6',                 // Blue - Documentation phase
      'Requiring Approval': '#F97316',            // Orange - Needs attention/review
      'Ready for Dev': '#059669',                 // Green - Approved, ready to start
      'Being Built': '#8B5CF6',                   // Purple - Development in progress
      'Ready for Test': '#D97706',                // Amber - Ready for validation
      'Under Test': '#DC2626',                    // Red - Critical testing phase
      'Testing Satisfactory': '#16A34A',          // Green - Tests passed
      'Shipped/Deployed to Customer': '#15803D',  // Dark Green - Final success

      // Feature states
      'NEW': '#6B7280',                           // Gray - Initial state
      'OPEN': '#3B82F6',                          // Blue - Active work
      'CLOSED': '#15803D'                         // Dark Green - Completed
    };
    return colors[state] || '#6B7280';
  };

  if (!transitions) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        {showStateChip && (
          <Chip
            label={currentState}
            sx={{
              backgroundColor: getStateColor(currentState),
              color: 'white',
              fontWeight: 600,
              fontSize: '0.75rem'
            }}
            size={size}
          />
        )}
        <CircularProgress size={16} />
      </Box>
    );
  }

  return (
    <Box sx={{
      display: 'flex',
      alignItems: 'center',
      gap: 1
    }}>
      {/* Previous State Button */}
      {transitions.previous && (
        <Tooltip
          title={
            transitions.canTransitionPrevious?.canTransition
              ? `Back to ${transitions.previous}`
              : transitions.canTransitionPrevious?.reason
          }
        >
          <span>
            <Button
              variant="outlined"
              size="small"
              startIcon={<ArrowBackIcon />}
              onClick={() => transitions.canTransitionPrevious?.canTransition && !disabled && !loading && handleTransition(transitions.previous, 'previous')}
              disabled={!transitions.canTransitionPrevious?.canTransition || disabled || loading}
              sx={{
                borderColor: transitions.canTransitionPrevious?.canTransition && !disabled ? 'primary.main' : 'divider',
                color: transitions.canTransitionPrevious?.canTransition && !disabled ? 'primary.main' : 'text.disabled',
                fontSize: '0.75rem',
                minWidth: 'auto',
                px: 1.5,
                py: 0.5,
                '&:hover': {
                  borderColor: 'primary.main',
                  backgroundColor: 'rgba(25, 118, 210, 0.1)',
                  boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)'
                },
                '&:disabled': {
                  borderColor: 'divider',
                  color: 'text.disabled'
                }
              }}
            >
              {transitions.previous}
            </Button>
          </span>
        </Tooltip>
      )}

      {/* Current State Badge */}
      {showStateChip && (
        <Chip
          label={currentState}
          sx={{
            backgroundColor: getStateColor(currentState),
            color: 'white',
            fontWeight: 600,
            fontSize: '0.75rem',
            height: 32,
            mx: 1,
            borderRadius: '16px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            '& .MuiChip-label': {
              px: 2
            }
          }}
        />
      )}

      {/* Next State Button */}
      {transitions.next && (
        <Tooltip
          title={
            transitions.canTransitionNext?.canTransition
              ? `Advance to ${transitions.next}`
              : transitions.canTransitionNext?.reason
          }
        >
          <span>
            <Button
              variant={transitions.canTransitionNext?.canTransition && !disabled ? "contained" : "outlined"}
              size="small"
              endIcon={<ArrowForwardIcon />}
              onClick={() => transitions.canTransitionNext?.canTransition && !disabled && !loading && handleTransition(transitions.next, 'next')}
              disabled={!transitions.canTransitionNext?.canTransition || disabled || loading}
              sx={{
                backgroundColor: transitions.canTransitionNext?.canTransition && !disabled ? 'primary.main' : 'transparent',
                borderColor: transitions.canTransitionNext?.canTransition && !disabled ? 'primary.main' : 'divider',
                color: transitions.canTransitionNext?.canTransition && !disabled
                  ? 'white'
                  : 'text.disabled',
                fontSize: '0.75rem',
                minWidth: 'auto',
                px: 1.5,
                py: 0.5,
                '&:hover': {
                  backgroundColor: transitions.canTransitionNext?.canTransition && !disabled ? 'primary.dark' : 'rgba(25, 118, 210, 0.1)',
                  borderColor: 'primary.main',
                  boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)'
                },
                '&:disabled': {
                  backgroundColor: 'transparent',
                  borderColor: 'divider',
                  color: 'text.disabled'
                }
              }}
            >
              {transitions.next}
            </Button>
          </span>
        </Tooltip>
      )}

      {loading && (
        <CircularProgress size={16} sx={{ ml: 1 }} />
      )}
    </Box>
  );
};

export default StateTransitionControl;
