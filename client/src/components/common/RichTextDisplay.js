import React from 'react';
import { Box } from '@mui/material';

const RichTextDisplay = ({ content, sx = {}, inline = false }) => {
  if (!content) {
    return null;
  }

  // For inline usage (inside Typography/ListItemText secondary), use span
  if (inline) {
    return (
      <span
        style={{
          fontSize: 'inherit',
          color: 'inherit',
          ...sx,
        }}
        dangerouslySetInnerHTML={{ __html: content }}
      />
    );
  }

  // For block usage, use Box as before
  return (
    <Box
      sx={{
        '& img': {
          maxWidth: '100%',
          height: 'auto',
        },
        '& p': {
          margin: '0 0 1em 0',
          '&:last-child': {
            marginBottom: 0,
          },
        },
        '& ul, & ol': {
          paddingLeft: '1.5em',
          margin: '0 0 1em 0',
        },
        '& li': {
          marginBottom: '0.25em',
        },
        '& h1, & h2, & h3, & h4, & h5, & h6': {
          margin: '1em 0 0.5em 0',
          '&:first-child': {
            marginTop: 0,
          },
        },
        '& blockquote': {
          borderLeft: '4px solid #e0e0e0',
          paddingLeft: '1em',
          margin: '1em 0',
          fontStyle: 'italic',
        },
        '& code': {
          backgroundColor: '#f5f5f5',
          padding: '0.2em 0.4em',
          borderRadius: '3px',
          fontFamily: 'monospace',
        },
        '& pre': {
          backgroundColor: '#f5f5f5',
          padding: '1em',
          borderRadius: '4px',
          overflow: 'auto',
          '& code': {
            backgroundColor: 'transparent',
            padding: 0,
          },
        },
        '& table': {
          borderCollapse: 'collapse',
          width: '100%',
          margin: '1em 0',
        },
        '& th, & td': {
          border: '1px solid #e0e0e0',
          padding: '0.5em',
          textAlign: 'left',
        },
        '& th': {
          backgroundColor: '#f5f5f5',
          fontWeight: 'bold',
        },
        ...sx,
      }}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
};

export default RichTextDisplay;
