import React, { useState, useRef, useEffect } from 'react';
import { Box, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography, Tooltip } from '@mui/material';
import { MoreHoriz as MoreIcon } from '@mui/icons-material';
import RichTextDisplay from './RichTextDisplay';

const TruncatedRichTextDisplay = ({ content, title = "Full Description", sx = {} }) => {
  const [showFullDialog, setShowFullDialog] = useState(false);
  const [isTruncated, setIsTruncated] = useState(false);
  const contentRef = useRef(null);

  useEffect(() => {
    if (contentRef.current) {
      // Check if content is overflowing (more than 2 lines)
      const element = contentRef.current;
      const lineHeight = parseInt(window.getComputedStyle(element).lineHeight);
      const maxHeight = lineHeight * 2; // 2 lines
      setIsTruncated(element.scrollHeight > maxHeight);
    }
  }, [content]);

  if (!content) {
    return null;
  }

  const handleSeeMoreClick = (e) => {
    e.stopPropagation(); // Prevent parent click events
    setShowFullDialog(true);
  };

  const handleCloseDialog = (e) => {
    e?.stopPropagation(); // Prevent parent click events when closing dialog
    setShowFullDialog(false);
  };

  return (
    <>
      <Box
        sx={{
          position: 'relative',
          display: 'flex',
          alignItems: 'flex-start',
          gap: 1,
          ...sx
        }}
      >
        <Box
          ref={contentRef}
          sx={{
            overflow: 'hidden',
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            lineHeight: '1.5em',
            maxHeight: '3.4em', // 2 lines * 1.5em + buffer for descenders (p, g, y, j, q)
            flex: 1,
            '& img': {
              maxWidth: '100%',
              height: 'auto',
            },
            '& p': {
              margin: '0 0 0.5em 0',
              '&:last-child': {
                marginBottom: 0,
              },
            },
            '& ul, & ol': {
              paddingLeft: '1.5em',
              margin: '0 0 0.5em 0',
            },
            '& li': {
              marginBottom: '0.25em',
            },
            '& h1, & h2, & h3, & h4, & h5, & h6': {
              margin: '0.5em 0 0.25em 0',
              '&:first-child': {
                marginTop: 0,
              },
            },
            '& blockquote': {
              borderLeft: '4px solid #e0e0e0',
              paddingLeft: '1em',
              margin: '0.5em 0',
              fontStyle: 'italic',
            },
            '& code': {
              backgroundColor: '#f5f5f5',
              padding: '0.2em 0.4em',
              borderRadius: '3px',
              fontFamily: 'monospace',
            },
            '& pre': {
              backgroundColor: '#f5f5f5',
              padding: '1em',
              borderRadius: '4px',
              overflow: 'auto',
              '& code': {
                backgroundColor: 'transparent',
                padding: 0,
              },
            },
          }}
          dangerouslySetInnerHTML={{ __html: content }}
        />
        {isTruncated && (
          <Tooltip title="View full description" arrow>
            <IconButton
              size="small"
              onClick={handleSeeMoreClick}
              sx={{
                color: 'primary.main',
                p: 0.5,
                mt: -0.5, // Align with first line of text
                '&:hover': {
                  backgroundColor: 'primary.light',
                  color: 'primary.contrastText',
                },
              }}
            >
              <MoreIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        )}
      </Box>

      {/* Full Content Dialog */}
      <Dialog
        open={showFullDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        scroll="paper"
        onClick={(e) => e.stopPropagation()} // Prevent any clicks in dialog from bubbling up
      >
        <DialogTitle>{title}</DialogTitle>
        <DialogContent dividers>
          <RichTextDisplay content={content} />
        </DialogContent>
        <DialogActions>
          <Button
            onClick={(e) => {
              e.stopPropagation();
              handleCloseDialog(e);
            }}
            color="primary"
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default TruncatedRichTextDisplay;
