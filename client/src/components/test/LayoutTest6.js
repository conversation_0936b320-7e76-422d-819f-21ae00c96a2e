import React, { useState, useCallback, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  IconButton,
  Menu,
  MenuItem,
  FormControlLabel,
  Switch,
  Divider,
  Chip,
  List,
  ListItem,
  ListItemText,
  TextField,
  Button,
  Fade,
  useTheme,
  alpha,
  Alert
} from '@mui/material';
import {
  Settings as SettingsIcon,
  DragIndicator as DragIcon,
  Description as DescriptionIcon,
  Chat as ChatIcon,
  Group as GroupIcon,
  LocalOffer as TagIcon,
  Assignment as TestIcon,
  History as HistoryIcon,
  Warning as WarningIcon
} from '@mui/icons-material';

// Mock data (same as LayoutTest5)
const mockData = {
  description: `
    <h3>E-commerce Platform Enhancement</h3>
    <p>This feature focuses on improving the user experience for our online shopping platform. 
    Key improvements include enhanced product search, streamlined checkout process, and improved mobile responsiveness.</p>
    <ul>
      <li>Advanced filtering and sorting capabilities</li>
      <li>One-click checkout for returning customers</li>
      <li>Mobile-first responsive design</li>
      <li>Real-time inventory updates</li>
    </ul>
  `,
  comments: [
    { id: 1, author: '<PERSON>', text: 'The mobile responsiveness is crucial for our Q4 goals.', timestamp: '2 hours ago', avatar: 'SC' },
    { id: 2, author: '<PERSON>', text: 'Should we consider A/B testing the checkout flow?', timestamp: '1 hour ago', avatar: 'MJ' },
    { id: 3, author: 'Lisa Wang', text: 'Real-time inventory will require backend changes.', timestamp: '30 minutes ago', avatar: 'LW' }
  ],
  users: [
    { id: 1, name: 'Sarah Chen', role: 'Product Manager', status: 'online' },
    { id: 2, name: 'Mike Johnson', role: 'Developer', status: 'away' },
    { id: 3, name: 'Lisa Wang', role: 'Designer', status: 'online' },
    { id: 4, name: 'Tom Brown', role: 'QA Engineer', status: 'offline' }
  ],
  tests: [
    { id: 1, name: 'User Authentication', result: 'Pass', notes: 'All scenarios passed' },
    { id: 2, name: 'Product Search', result: 'Fail', notes: 'Timeout on large datasets' },
    { id: 3, name: 'Checkout Flow', result: 'Pass', notes: 'Performance improved' },
    { id: 4, name: 'Mobile UI', result: 'Pending', notes: 'Awaiting device testing' }
  ],
  tags: [
    { id: 1, name: 'v2.1', color: '#1976d2' },
    { id: 2, name: 'MVP', color: '#388e3c' },
    { id: 3, name: 'Mobile', color: '#f57c00' }
  ],
  documents: [
    { id: 1, name: 'Requirements v2.1', type: 'SRD', date: '2024-01-10' },
    { id: 2, name: 'Release Notes v2.0', type: 'Release', date: '2024-01-05' }
  ]
};

// Module definitions with order
const initialModules = [
  { id: 'description', name: 'Description', icon: DescriptionIcon, enabled: true, order: 0 },
  { id: 'comments', name: 'Comments', icon: ChatIcon, enabled: true, order: 1 },
  { id: 'userManagement', name: 'User Management', icon: GroupIcon, enabled: false, order: 2 },
  { id: 'testManagement', name: 'Test Management', icon: TestIcon, enabled: false, order: 3 },
  { id: 'releaseManagement', name: 'Release Management', icon: TagIcon, enabled: false, order: 4 },
  { id: 'documentHistory', name: 'Document History', icon: HistoryIcon, enabled: false, order: 5 }
];

// Professional drag handle with six dots
const DragHandle = () => {
  const theme = useTheme();
  
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: 20,
        height: 20,
        cursor: 'grab',
        color: theme.palette.text.secondary,
        transition: 'all 0.2s ease',
        '&:hover': {
          color: theme.palette.primary.main,
          transform: 'scale(1.1)'
        },
        '&:active': {
          cursor: 'grabbing'
        }
      }}
    >
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: 'repeat(2, 4px)',
          gridTemplateRows: 'repeat(3, 4px)',
          gap: '2px'
        }}
      >
        {[...Array(6)].map((_, i) => (
          <Box
            key={i}
            sx={{
              width: 4,
              height: 4,
              borderRadius: '50%',
              backgroundColor: 'currentColor'
            }}
          />
        ))}
      </Box>
    </Box>
  );
};

// Simple draggable module component (React 18 compatible)
const DraggableModule = ({ module, index, children, onDragStart, onDragEnd, onDragOver, isDragging }) => {
  const theme = useTheme();
  const IconComponent = module.icon;

  const handleDragStart = (e) => {
    e.dataTransfer.setData('text/plain', module.id);
    e.dataTransfer.effectAllowed = 'move';
    onDragStart(module.id, index);
  };

  const handleDragEnd = (e) => {
    onDragEnd();
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    onDragOver(index);
  };

  const handleDrop = (e) => {
    e.preventDefault();
  };

  return (
    <div
      draggable
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
      style={{
        marginBottom: theme.spacing(3)
      }}
    >
      <Paper
        sx={{
          borderRadius: 3,
          overflow: 'hidden',
          transition: isDragging ? 'none' : 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          transform: isDragging ? 'scale(1.02) rotate(1deg)' : 'scale(1)',
          opacity: isDragging ? 0.5 : 1,
          boxShadow: isDragging
            ? `0 20px 40px ${alpha(theme.palette.common.black, 0.3)}`
            : theme.shadows[2],
          '&:hover': {
            boxShadow: isDragging ? undefined : theme.shadows[8],
            transform: isDragging ? undefined : 'translateY(-2px)'
          }
        }}
      >
        {/* Module Header with drag handle */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            p: 2,
            borderBottom: 1,
            borderColor: 'divider',
            bgcolor: alpha(theme.palette.primary.main, 0.02),
            cursor: 'grab',
            '&:active': {
              cursor: 'grabbing'
            }
          }}
        >
          <DragHandle />
          <IconComponent
            sx={{
              color: theme.palette.primary.main,
              fontSize: 20
            }}
          />
          <Typography
            variant="subtitle2"
            sx={{
              fontWeight: 600,
              color: theme.palette.text.primary
            }}
          >
            {module.name}
          </Typography>
        </Box>

        {/* Module Content */}
        <Box sx={{ p: 2 }}>
          {children}
        </Box>
      </Paper>
    </div>
  );
};

const LayoutTest6 = () => {
  const [modules, setModules] = useState(initialModules);
  const [settingsAnchor, setSettingsAnchor] = useState(null);
  const [newComment, setNewComment] = useState('');
  const [draggedItem, setDraggedItem] = useState(null);
  const [draggedIndex, setDraggedIndex] = useState(null);
  const [dragOverIndex, setDragOverIndex] = useState(null);
  const theme = useTheme();

  const handleSettingsClick = (event) => {
    setSettingsAnchor(event.currentTarget);
  };

  const handleSettingsClose = () => {
    setSettingsAnchor(null);
  };

  const toggleModule = (moduleId) => {
    setModules(prev => prev.map(module =>
      module.id === moduleId
        ? { ...module, enabled: !module.enabled }
        : module
    ));
  };

  const handleDragStart = (moduleId, index) => {
    setDraggedItem(moduleId);
    setDraggedIndex(index);
  };

  const handleDragEnd = () => {
    if (draggedIndex !== null && dragOverIndex !== null && draggedIndex !== dragOverIndex) {
      const enabledModules = modules.filter(m => m.enabled);
      const newOrder = [...enabledModules];
      const [draggedModule] = newOrder.splice(draggedIndex, 1);
      newOrder.splice(dragOverIndex, 0, draggedModule);

      // Update the modules array with new order
      const newModules = modules.map(module => {
        if (!module.enabled) return module;
        const newIndex = newOrder.findIndex(m => m.id === module.id);
        return { ...module, order: newIndex };
      });

      setModules(newModules);
    }

    setDraggedItem(null);
    setDraggedIndex(null);
    setDragOverIndex(null);
  };

  const handleDragOver = (index) => {
    if (draggedIndex !== null && index !== draggedIndex) {
      setDragOverIndex(index);
    }
  };

  const enabledModules = modules
    .filter(m => m.enabled)
    .sort((a, b) => a.order - b.order);

  const renderModuleContent = (module) => {
    switch (module.id) {
      case 'description':
        return (
          <Box>
            <div dangerouslySetInnerHTML={{ __html: mockData.description }} />
          </Box>
        );

      case 'comments':
        return (
          <Box>
            <List dense sx={{ mb: 2 }}>
              {mockData.comments.map((comment) => (
                <ListItem key={comment.id} sx={{ px: 0 }}>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Chip label={comment.avatar} size="small" />
                        <Typography variant="body2" fontWeight={500}>
                          {comment.author}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {comment.timestamp}
                        </Typography>
                      </Box>
                    }
                    secondary={comment.text}
                  />
                </ListItem>
              ))}
            </List>
            <TextField
              fullWidth
              size="small"
              placeholder="Add a comment..."
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              multiline
              rows={2}
            />
          </Box>
        );

      case 'userManagement':
        return (
          <List dense>
            {mockData.users.map((user) => (
              <ListItem key={user.id} sx={{ px: 0 }}>
                <ListItemText
                  primary={user.name}
                  secondary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="caption">{user.role}</Typography>
                      <Chip
                        label={user.status}
                        size="small"
                        color={user.status === 'online' ? 'success' : 'default'}
                      />
                    </Box>
                  }
                />
              </ListItem>
            ))}
          </List>
        );

      case 'testManagement':
        return (
          <Box>
            <Typography variant="h6" gutterBottom>Test Results</Typography>
            {mockData.tests.map((test) => (
              <Box key={test.id} sx={{ mb: 2, p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                  <Typography variant="subtitle2">{test.name}</Typography>
                  <Chip
                    label={test.result}
                    color={
                      test.result === 'Pass' ? 'success' :
                      test.result === 'Fail' ? 'error' : 'default'
                    }
                    size="small"
                  />
                </Box>
                <Typography variant="body2" color="text.secondary">
                  {test.notes}
                </Typography>
              </Box>
            ))}
          </Box>
        );

      case 'releaseManagement':
        return (
          <Box>
            <Typography variant="h6" gutterBottom>Release Tags</Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {mockData.tags.map((tag) => (
                <Chip
                  key={tag.id}
                  label={tag.name}
                  sx={{ 
                    bgcolor: alpha(tag.color, 0.1),
                    color: tag.color,
                    border: `1px solid ${alpha(tag.color, 0.3)}`
                  }}
                />
              ))}
            </Box>
          </Box>
        );

      case 'documentHistory':
        return (
          <Box>
            <Typography variant="h6" gutterBottom>Documents</Typography>
            <List dense>
              {mockData.documents.map((doc) => (
                <ListItem key={doc.id} sx={{ px: 0 }}>
                  <ListItemText
                    primary={doc.name}
                    secondary={`${doc.type} • ${doc.date}`}
                  />
                </ListItem>
              ))}
            </List>
          </Box>
        );

      default:
        return (
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            height: 100,
            color: 'text.secondary'
          }}>
            <Typography variant="body2">
              Module content placeholder
            </Typography>
          </Box>
        );
    }
  };

  return (
    <Box sx={{ p: 3, bgcolor: 'background.default', minHeight: '100vh' }}>
      {/* Header */}
      <Paper sx={{ p: 3, mb: 3, borderRadius: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h4" gutterBottom>
              Layout Test 6: HTML5 Drag & Drop
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Testing modular layout with native HTML5 drag and drop API (React 18 compatible)
            </Typography>
          </Box>
          <IconButton onClick={handleSettingsClick}>
            <SettingsIcon />
          </IconButton>
        </Box>
      </Paper>

      {/* Compatibility Notice */}
      <Alert
        severity="info"
        icon={<WarningIcon />}
        sx={{ mb: 3, borderRadius: 2 }}
      >
        <Typography variant="body2">
          <strong>Note:</strong> This test uses native HTML5 drag & drop instead of react-beautiful-dnd
          due to React 18 StrictMode compatibility issues.
        </Typography>
      </Alert>

      {/* Settings Menu */}
      <Menu
        anchorEl={settingsAnchor}
        open={Boolean(settingsAnchor)}
        onClose={handleSettingsClose}
        PaperProps={{
          sx: { minWidth: 250, borderRadius: 2 }
        }}
      >
        <Typography variant="subtitle2" sx={{ px: 2, py: 1, fontWeight: 600 }}>
          Module Visibility
        </Typography>
        <Divider />
        {modules.map((module) => (
          <MenuItem key={module.id} onClick={() => toggleModule(module.id)}>
            <FormControlLabel
              control={
                <Switch
                  checked={module.enabled}
                  size="small"
                />
              }
              label={module.name}
              sx={{ width: '100%', m: 0 }}
            />
          </MenuItem>
        ))}
      </Menu>

      {/* Draggable Modules */}
      <Box
        sx={{
          minHeight: 400,
          transition: 'background-color 0.2s ease',
          bgcolor: draggedItem
            ? alpha(theme.palette.primary.main, 0.05)
            : 'transparent',
          borderRadius: 2,
          p: draggedItem ? 2 : 0
        }}
      >
        {enabledModules.length === 0 ? (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: 300,
              border: `2px dashed ${alpha(theme.palette.primary.main, 0.3)}`,
              borderRadius: 3,
              color: 'text.secondary'
            }}
          >
            <SettingsIcon sx={{ fontSize: 48, mb: 2, opacity: 0.5 }} />
            <Typography variant="h6" gutterBottom>
              No modules enabled
            </Typography>
            <Typography variant="body2">
              Click the settings icon to enable modules
            </Typography>
          </Box>
        ) : (
          enabledModules.map((module, index) => (
            <DraggableModule
              key={module.id}
              module={module}
              index={index}
              onDragStart={handleDragStart}
              onDragEnd={handleDragEnd}
              onDragOver={handleDragOver}
              isDragging={draggedItem === module.id}
            >
              {renderModuleContent(module)}
            </DraggableModule>
          ))
        )}
      </Box>
    </Box>
  );
};

export default LayoutTest6;
