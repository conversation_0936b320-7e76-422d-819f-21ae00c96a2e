import React, { useState, useCallback } from 'react';
import {
  Box,
  Paper,
  Typography,
  IconButton,
  Menu,
  MenuItem,
  FormControlLabel,
  Switch,
  Divider,
  Chip,
  List,
  ListItem,
  ListItemText,
  TextField,
  Button,
  Fade,
  Zoom,
  useTheme,
  alpha
} from '@mui/material';
import {
  Settings as SettingsIcon,
  DragIndicator as DragIcon,
  Description as DescriptionIcon,
  Chat as ChatIcon,
  Group as GroupIcon,
  LocalOffer as TagIcon,
  Assignment as TestIcon,
  History as HistoryIcon
} from '@mui/icons-material';
import { DataGrid } from '@mui/x-data-grid';

// Mock data
const mockData = {
  description: `
    <h3>E-commerce Platform Enhancement</h3>
    <p>This feature focuses on improving the user experience for our online shopping platform. 
    Key improvements include enhanced product search, streamlined checkout process, and improved mobile responsiveness.</p>
    <ul>
      <li>Advanced filtering and sorting capabilities</li>
      <li>One-click checkout for returning customers</li>
      <li>Mobile-first responsive design</li>
      <li>Real-time inventory updates</li>
    </ul>
  `,
  comments: [
    { id: 1, author: '<PERSON>', text: 'The mobile responsiveness is crucial for our Q4 goals.', timestamp: '2 hours ago', avatar: 'SC' },
    { id: 2, author: '<PERSON>', text: 'Should we consider A/B testing the checkout flow?', timestamp: '1 hour ago', avatar: 'MJ' },
    { id: 3, author: 'Lisa Wang', text: 'Real-time inventory will require backend changes.', timestamp: '30 minutes ago', avatar: 'LW' }
  ],
  users: [
    { id: 1, name: 'Sarah Chen', role: 'Product Manager', status: 'online' },
    { id: 2, name: 'Mike Johnson', role: 'Developer', status: 'away' },
    { id: 3, name: 'Lisa Wang', role: 'Designer', status: 'online' },
    { id: 4, name: 'Tom Brown', role: 'QA Engineer', status: 'offline' }
  ],
  tests: [
    { id: 1, name: 'User Authentication', result: 'Pass', lastRun: '2024-01-15', notes: 'All scenarios passed' },
    { id: 2, name: 'Product Search', result: 'Fail', lastRun: '2024-01-14', notes: 'Timeout on large datasets' },
    { id: 3, name: 'Checkout Flow', result: 'Pass', lastRun: '2024-01-15', notes: 'Performance improved' },
    { id: 4, name: 'Mobile UI', result: 'Pending', lastRun: null, notes: 'Awaiting device testing' }
  ],
  tags: [
    { id: 1, name: 'v2.1', color: '#1976d2' },
    { id: 2, name: 'MVP', color: '#388e3c' },
    { id: 3, name: 'Mobile', color: '#f57c00' }
  ],
  documents: [
    { id: 1, name: 'Requirements v2.1', type: 'SRD', date: '2024-01-10' },
    { id: 2, name: 'Release Notes v2.0', type: 'Release', date: '2024-01-05' }
  ]
};

// Module definitions
const moduleDefinitions = [
  { id: 'description', name: 'Description', icon: DescriptionIcon, enabled: true },
  { id: 'comments', name: 'Comments', icon: ChatIcon, enabled: true },
  { id: 'userManagement', name: 'User Management', icon: GroupIcon, enabled: false },
  { id: 'testManagement', name: 'Test Management', icon: TestIcon, enabled: false },
  { id: 'releaseManagement', name: 'Release Management', icon: TagIcon, enabled: false },
  { id: 'documentHistory', name: 'Document History', icon: HistoryIcon, enabled: false }
];

// Professional drag handle component
const DragHandle = ({ isDragging }) => {
  const theme = useTheme();
  
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: 20,
        height: 20,
        cursor: 'grab',
        color: theme.palette.text.secondary,
        opacity: isDragging ? 0.5 : 0.7,
        transition: 'all 0.2s ease',
        '&:hover': {
          opacity: 1,
          color: theme.palette.primary.main,
          transform: 'scale(1.1)'
        },
        '&:active': {
          cursor: 'grabbing'
        }
      }}
    >
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: 'repeat(2, 4px)',
          gridTemplateRows: 'repeat(3, 4px)',
          gap: '2px'
        }}
      >
        {[...Array(6)].map((_, i) => (
          <Box
            key={i}
            sx={{
              width: 4,
              height: 4,
              borderRadius: '50%',
              backgroundColor: 'currentColor'
            }}
          />
        ))}
      </Box>
    </Box>
  );
};

// Module component with enhanced styling
const ModuleCard = ({ module, children, isDragging, style }) => {
  const theme = useTheme();
  const IconComponent = module.icon;
  
  return (
    <Zoom in={true} timeout={300}>
      <Paper
        sx={{
          ...style,
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          borderRadius: 3,
          overflow: 'hidden',
          transition: isDragging ? 'none' : 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          transform: isDragging ? 'scale(1.02) rotate(2deg)' : 'scale(1)',
          boxShadow: isDragging 
            ? `0 20px 40px ${alpha(theme.palette.common.black, 0.3)}`
            : theme.shadows[2],
          '&:hover': {
            boxShadow: isDragging ? undefined : theme.shadows[8],
            transform: isDragging ? undefined : 'translateY(-2px)'
          }
        }}
      >
        {/* Module Header */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            p: 2,
            borderBottom: 1,
            borderColor: 'divider',
            bgcolor: alpha(theme.palette.primary.main, 0.02)
          }}
        >
          <DragHandle isDragging={isDragging} />
          <IconComponent 
            sx={{ 
              color: theme.palette.primary.main,
              fontSize: 20
            }} 
          />
          <Typography 
            variant="subtitle2" 
            sx={{ 
              fontWeight: 600,
              color: theme.palette.text.primary
            }}
          >
            {module.name}
          </Typography>
        </Box>
        
        {/* Module Content */}
        <Box sx={{ flex: 1, p: 2, overflow: 'auto' }}>
          {children}
        </Box>
      </Paper>
    </Zoom>
  );
};

const LayoutTest5 = () => {
  const [modules, setModules] = useState(moduleDefinitions);
  const [settingsAnchor, setSettingsAnchor] = useState(null);
  const [newComment, setNewComment] = useState('');
  const theme = useTheme();

  const handleSettingsClick = (event) => {
    setSettingsAnchor(event.currentTarget);
  };

  const handleSettingsClose = () => {
    setSettingsAnchor(null);
  };

  const toggleModule = (moduleId) => {
    setModules(prev => prev.map(module => 
      module.id === moduleId 
        ? { ...module, enabled: !module.enabled }
        : module
    ));
  };

  const enabledModules = modules.filter(m => m.enabled);

  // Test data columns for DataGrid
  const testColumns = [
    { field: 'name', headerName: 'Test Name', flex: 1 },
    { 
      field: 'result', 
      headerName: 'Result', 
      width: 120,
      renderCell: (params) => (
        <Chip
          label={params.value}
          color={
            params.value === 'Pass' ? 'success' :
            params.value === 'Fail' ? 'error' : 'default'
          }
          size="small"
        />
      )
    },
    { field: 'lastRun', headerName: 'Last Run', width: 120 },
    { field: 'notes', headerName: 'Notes', flex: 1 }
  ];

  const renderModuleContent = (module) => {
    switch (module.id) {
      case 'description':
        return (
          <Box>
            <div dangerouslySetInnerHTML={{ __html: mockData.description }} />
          </Box>
        );

      case 'comments':
        return (
          <Box>
            <List dense sx={{ mb: 2 }}>
              {mockData.comments.map((comment) => (
                <ListItem key={comment.id} sx={{ px: 0 }}>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Chip label={comment.avatar} size="small" />
                        <Typography variant="body2" fontWeight={500}>
                          {comment.author}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {comment.timestamp}
                        </Typography>
                      </Box>
                    }
                    secondary={comment.text}
                  />
                </ListItem>
              ))}
            </List>
            <TextField
              fullWidth
              size="small"
              placeholder="Add a comment..."
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              multiline
              rows={2}
            />
          </Box>
        );

      case 'userManagement':
        return (
          <List dense>
            {mockData.users.map((user) => (
              <ListItem key={user.id} sx={{ px: 0 }}>
                <ListItemText
                  primary={user.name}
                  secondary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="caption">{user.role}</Typography>
                      <Chip
                        label={user.status}
                        size="small"
                        color={user.status === 'online' ? 'success' : 'default'}
                      />
                    </Box>
                  }
                />
              </ListItem>
            ))}
          </List>
        );

      case 'testManagement':
        return (
          <Box sx={{ height: 300 }}>
            <DataGrid
              rows={mockData.tests}
              columns={testColumns}
              hideFooter
              disableRowSelectionOnClick
              sx={{
                border: 'none',
                '& .MuiDataGrid-cell': {
                  borderBottom: `1px solid ${alpha(theme.palette.divider, 0.5)}`
                }
              }}
            />
          </Box>
        );

      case 'releaseManagement':
        return (
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {mockData.tags.map((tag) => (
              <Chip
                key={tag.id}
                label={tag.name}
                sx={{ 
                  bgcolor: alpha(tag.color, 0.1),
                  color: tag.color,
                  border: `1px solid ${alpha(tag.color, 0.3)}`
                }}
              />
            ))}
          </Box>
        );

      case 'documentHistory':
        return (
          <List dense>
            {mockData.documents.map((doc) => (
              <ListItem key={doc.id} sx={{ px: 0 }}>
                <ListItemText
                  primary={doc.name}
                  secondary={`${doc.type} • ${doc.date}`}
                />
              </ListItem>
            ))}
          </List>
        );

      default:
        return (
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            height: 100,
            color: 'text.secondary'
          }}>
            <Typography variant="body2">
              Module content placeholder
            </Typography>
          </Box>
        );
    }
  };

  return (
    <Box sx={{ p: 3, bgcolor: 'background.default', minHeight: '100vh' }}>
      {/* Header */}
      <Paper sx={{ p: 3, mb: 3, borderRadius: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h4" gutterBottom>
              Layout Test 5: MUI X DataGrid System
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Testing modular layout with MUI X DataGrid and enhanced animations
            </Typography>
          </Box>
          <IconButton onClick={handleSettingsClick}>
            <SettingsIcon />
          </IconButton>
        </Box>
      </Paper>

      {/* Settings Menu */}
      <Menu
        anchorEl={settingsAnchor}
        open={Boolean(settingsAnchor)}
        onClose={handleSettingsClose}
        PaperProps={{
          sx: { minWidth: 250, borderRadius: 2 }
        }}
      >
        <Typography variant="subtitle2" sx={{ px: 2, py: 1, fontWeight: 600 }}>
          Module Visibility
        </Typography>
        <Divider />
        {modules.map((module) => (
          <MenuItem key={module.id} onClick={() => toggleModule(module.id)}>
            <FormControlLabel
              control={
                <Switch
                  checked={module.enabled}
                  size="small"
                />
              }
              label={module.name}
              sx={{ width: '100%', m: 0 }}
            />
          </MenuItem>
        ))}
      </Menu>

      {/* Modules Grid */}
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
          gap: 3,
          minHeight: 400
        }}
      >
        {enabledModules.map((module) => (
          <Fade key={module.id} in={true} timeout={500}>
            <div>
              <ModuleCard module={module}>
                {renderModuleContent(module)}
              </ModuleCard>
            </div>
          </Fade>
        ))}
        
        {/* Empty state */}
        {enabledModules.length === 0 && (
          <Box
            sx={{
              gridColumn: '1 / -1',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: 300,
              border: `2px dashed ${alpha(theme.palette.primary.main, 0.3)}`,
              borderRadius: 3,
              color: 'text.secondary'
            }}
          >
            <SettingsIcon sx={{ fontSize: 48, mb: 2, opacity: 0.5 }} />
            <Typography variant="h6" gutterBottom>
              No modules enabled
            </Typography>
            <Typography variant="body2">
              Click the settings icon to enable modules
            </Typography>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default LayoutTest5;
