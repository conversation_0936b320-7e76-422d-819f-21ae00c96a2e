import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
  Paper,
  Box,
  Typography,
  IconButton,
  Chip,
  Collapse
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  ChevronRight as ChevronRightIcon,
  BugReport as TestIcon
} from '@mui/icons-material';
import TestTabs from './TestTabs';
import TestGrid from './TestGrid';
import TestToolbar from './TestToolbar';
import CopyTestsDialog from './CopyTestsDialog';
import './TestManagement.css';

const TestManagement = ({ requirement }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeTab, setActiveTab] = useState(1);
  const [testSuites, setTestSuites] = useState({});
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [loading, setLoading] = useState(false);
  const [copyDialogOpen, setCopyDialogOpen] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [originalTestSuites, setOriginalTestSuites] = useState({}); // Store original state for cancel
  const [testCounts, setTestCounts] = useState({ passCount: 0, failCount: 0, skipCount: 0, untestedCount: 0 });

  // Helper function to check if a test has meaningful content
  const hasTestContent = (test) => {
    return test.testName?.trim() ||
           test.setup?.trim() ||
           test.steps?.trim() ||
           test.expectedResult?.trim();
  };

  // Calculate test result counts from latest version only
  const calculateTestCounts = () => {
    let passCount = 0;
    let failCount = 0;
    let skipCount = 0;
    let untestedCount = 0;

    // Find the latest version (highest version number)
    const versions = Object.keys(testSuites).map(v => parseInt(v)).filter(v => !isNaN(v));
    if (versions.length === 0) {
      return { passCount, failCount, skipCount, untestedCount };
    }

    const latestVersion = Math.max(...versions);
    const latestTestSuite = testSuites[latestVersion];

    if (latestTestSuite && latestTestSuite.tests) {
      latestTestSuite.tests.forEach(test => {
        // Only count tests that have actual content
        if (!hasTestContent(test)) {
          return; // Skip empty tests
        }

        if (!test.results || test.results.length === 0) {
          // Has content but no results
          untestedCount++;
        } else {
          // Find the latest non-null result
          const latestResult = test.results
            .filter(result => result && result.status)
            .slice(-1)[0]; // Get the last valid result

          if (!latestResult) {
            untestedCount++;
          } else {
            switch (latestResult.status) {
              case 'Pass':
                passCount++;
                break;
              case 'Fail':
                failCount++;
                break;
              case 'Skip':
                skipCount++;
                break;
              default:
                untestedCount++;
            }
          }
        }
      });
    }

    return { passCount, failCount, skipCount, untestedCount };
  };

  // Recalculate test counts when test suites change
  useEffect(() => {
    const counts = calculateTestCounts();
    setTestCounts(counts);
    console.log('Test counts updated:', counts);
  }, [testSuites]);

  // Get available versions from requirement
  const availableVersions = requirement?.versions?.map(v => v.version) || [1];

  useEffect(() => {
    if (requirement) {
      // Load test suites for all versions to calculate counts
      availableVersions.forEach(version => {
        loadTestSuite(version);
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [requirement]);

  useEffect(() => {
    if (isExpanded && requirement) {
      loadTestSuite(activeTab);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isExpanded, activeTab, requirement]);

  const loadTestSuite = async (version) => {
    if (testSuites[version]) return; // Already loaded

    setLoading(true);
    try {
      const response = await axios.get(`/api/requirements/${requirement._id}/tests/${version}`);
      let testSuite = response.data;

      // If the test suite is empty or has no tests, add 3 empty rows
      if (!testSuite.tests || testSuite.tests.length === 0) {
        const initialTests = Array.from({ length: 3 }, (_, index) => ({
          id: `test_${version}_${Date.now()}_${index}`,
          testName: '',
          setup: '',
          steps: '',
          expectedResult: '',
          results: [null, null] // Start with 2 result columns
        }));
        testSuite = {
          ...testSuite,
          tests: initialTests
        };
      }

      setTestSuites(prev => ({
        ...prev,
        [version]: testSuite
      }));

      // Store original state for cancel functionality
      setOriginalTestSuites(prev => ({
        ...prev,
        [version]: JSON.parse(JSON.stringify(testSuite)) // Deep copy
      }));
    } catch (error) {
      console.error('Error loading test suite:', error);
      // If test suite doesn't exist, create one with 3 empty rows
      if (error.response?.status === 404) {
        const initialTests = Array.from({ length: 3 }, (_, index) => ({
          id: `test_${version}_${Date.now()}_${index}`,
          testName: '',
          setup: '',
          steps: '',
          expectedResult: '',
          results: [null, null] // Start with 2 result columns
        }));

        const newTestSuite = {
          version: version,
          tests: initialTests
        };

        setTestSuites(prev => ({
          ...prev,
          [version]: newTestSuite
        }));

        // Store original state for cancel functionality
        setOriginalTestSuites(prev => ({
          ...prev,
          [version]: JSON.parse(JSON.stringify(newTestSuite)) // Deep copy
        }));
      }
    } finally {
      setLoading(false);
    }
  };

  const handleTestDataChange = (version, newTests) => {
    setTestSuites(prev => ({
      ...prev,
      [version]: {
        ...prev[version],
        tests: newTests
      }
    }));
    setHasUnsavedChanges(true);
  };

  const handleSave = async () => {
    if (!hasUnsavedChanges) return;

    setLoading(true);
    try {
      const currentTestSuite = testSuites[activeTab];
      await axios.put(`/api/requirements/${requirement._id}/tests/${activeTab}`, {
        tests: currentTestSuite.tests
      });
      setHasUnsavedChanges(false);

      // Update the original state after successful save
      setOriginalTestSuites(prev => ({
        ...prev,
        [activeTab]: JSON.parse(JSON.stringify(currentTestSuite)) // Deep copy
      }));
    } catch (error) {
      console.error('Error saving test suite:', error);
      alert('Error saving tests. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    // Restore the original test suite state
    const originalTestSuite = originalTestSuites[activeTab];
    if (originalTestSuite) {
      setTestSuites(prev => ({
        ...prev,
        [activeTab]: JSON.parse(JSON.stringify(originalTestSuite)) // Deep copy to restore
      }));
    } else {
      // If no original state exists, reload from server
      setTestSuites(prev => {
        const newSuites = { ...prev };
        delete newSuites[activeTab];
        return newSuites;
      });
      loadTestSuite(activeTab);
    }
    setHasUnsavedChanges(false);
    setSelectedRows([]); // Clear any row selections
  };

  const handleAddRow = () => {
    const currentTestSuite = testSuites[activeTab];
    if (!currentTestSuite) {
      // If no test suite exists, create one with the new row
      const newTest = {
        id: `test_${activeTab}_${Date.now()}`,
        testName: '',
        setup: '',
        steps: '',
        expectedResult: '',
        results: [null, null] // Start with 2 result columns
      };

      setTestSuites(prev => ({
        ...prev,
        [activeTab]: {
          version: activeTab,
          tests: [newTest]
        }
      }));
      setHasUnsavedChanges(true);
      setSelectedRows([]); // Clear selection when creating new test suite
      return;
    }

    // Determine how many result columns we need based on existing tests
    const maxResults = currentTestSuite.tests.length > 0
      ? Math.max(...currentTestSuite.tests.map(test => test.results?.length || 0), 2)
      : 2;

    const newTest = {
      id: `test_${activeTab}_${Date.now()}`, // Use timestamp for unique ID
      testName: '',
      setup: '',
      steps: '',
      expectedResult: '',
      results: Array(maxResults).fill(null)
    };

    handleTestDataChange(activeTab, [...currentTestSuite.tests, newTest]);
    setSelectedRows([]); // Clear selection when adding new row
  };

  const handleAddColumn = () => {
    const currentTestSuite = testSuites[activeTab];
    if (!currentTestSuite) return;

    const updatedTests = currentTestSuite.tests.map(test => ({
      ...test,
      results: [...(test.results || []), null]
    }));

    handleTestDataChange(activeTab, updatedTests);
  };

  const handleCopyTests = (testsToCopy) => {
    const currentTestSuite = testSuites[activeTab];

    if (!currentTestSuite) {
      // If no test suite exists, create one with the copied tests
      setTestSuites(prev => ({
        ...prev,
        [activeTab]: {
          version: activeTab,
          tests: testsToCopy
        }
      }));
    } else {
      // Filter out empty tests from current suite and add copied tests
      const existingTestsWithContent = currentTestSuite.tests.filter(test => hasTestContent(test));
      const updatedTests = [...existingTestsWithContent, ...testsToCopy];

      // If we have no existing tests with content and no copied tests, add one empty row
      if (updatedTests.length === 0) {
        updatedTests.push({
          id: `test_${activeTab}_${Date.now()}`,
          testName: '',
          setup: '',
          steps: '',
          expectedResult: '',
          results: [null, null]
        });
      }

      handleTestDataChange(activeTab, updatedTests);
    }

    setHasUnsavedChanges(true);
  };

  const handleDeleteRow = () => {
    if (selectedRows.length === 0) return;

    const currentTestSuite = testSuites[activeTab];
    if (!currentTestSuite) return;

    // Filter out selected rows
    const updatedTests = currentTestSuite.tests.filter(test =>
      !selectedRows.includes(test.id)
    );

    // Ensure we always have at least one row
    if (updatedTests.length === 0) {
      updatedTests.push({
        id: `test_${activeTab}_${Date.now()}`,
        testName: '',
        setup: '',
        steps: '',
        expectedResult: '',
        results: [null, null]
      });
    }

    handleTestDataChange(activeTab, updatedTests);
    setSelectedRows([]); // Clear selection after deletion
  };

  const currentTestSuite = testSuites[activeTab];
  const { passCount, failCount, skipCount, untestedCount } = testCounts;

  return (
    <Paper sx={{
      p: 2,
      border: '1px solid',
      borderColor: 'divider',
      boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)'
    }}>
      {/* Collapsible Header */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: isExpanded ? 2 : 0,
          cursor: 'pointer',
          py: 1,
          '&:hover': {
            bgcolor: 'action.hover',
            borderRadius: 1
          }
        }}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <IconButton size="small" sx={{ p: 0 }}>
            {isExpanded ? <ExpandMoreIcon /> : <ChevronRightIcon />}
          </IconButton>
          <TestIcon sx={{ color: 'primary.main' }} />
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Test Management
          </Typography>
          {hasUnsavedChanges && (
            <Box
              sx={{
                width: 8,
                height: 8,
                borderRadius: '50%',
                bgcolor: 'warning.main',
                ml: 1
              }}
            />
          )}
          {!isExpanded && (
            <Box sx={{ display: 'flex', gap: 1, ml: 2 }}>
              <Chip
                label={`${passCount} passed`}
                size="small"
                variant="outlined"
                sx={{
                  borderColor: '#22c55e',
                  color: '#22c55e',
                  '& .MuiChip-label': { fontWeight: 600 }
                }}
              />
              <Chip
                label={`${failCount} failed`}
                size="small"
                variant="outlined"
                sx={{
                  borderColor: '#ef4444',
                  color: '#ef4444',
                  '& .MuiChip-label': { fontWeight: 600 }
                }}
              />
              <Chip
                label={`${skipCount} skipped`}
                size="small"
                variant="outlined"
                sx={{
                  borderColor: '#eab308',
                  color: '#eab308',
                  '& .MuiChip-label': { fontWeight: 600 }
                }}
              />
              <Chip
                label={`${untestedCount} untested`}
                size="small"
                variant="outlined"
                color="default"
              />
            </Box>
          )}
        </Box>
      </Box>

      {/* Collapsible Content */}
      <Collapse in={isExpanded} timeout="auto" unmountOnExit>
        <Box className="test-management-content">
          <TestTabs
            versions={availableVersions}
            activeTab={activeTab}
            onTabChange={setActiveTab}
          />

          <TestToolbar
            onSave={handleSave}
            onCancel={handleCancel}
            onAddRow={handleAddRow}
            onAddColumn={handleAddColumn}
            onCopyTests={() => setCopyDialogOpen(true)}
            onDeleteRow={handleDeleteRow}
            hasUnsavedChanges={hasUnsavedChanges}
            loading={loading}
            availableVersions={availableVersions}
            currentVersion={activeTab}
            selectedRows={selectedRows}
          />

          {loading ? (
            <Box sx={{ p: 2, textAlign: 'center' }}>Loading tests...</Box>
          ) : currentTestSuite ? (
            <TestGrid
              testSuite={currentTestSuite}
              onTestDataChange={(newTests) => handleTestDataChange(activeTab, newTests)}
              selectedRows={selectedRows}
              onSelectionChange={setSelectedRows}
            />
          ) : (
            <Box sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>
              No test data available
            </Box>
          )}
        </Box>
      </Collapse>

      {/* Copy Tests Dialog */}
      <CopyTestsDialog
        open={copyDialogOpen}
        onClose={() => setCopyDialogOpen(false)}
        onCopyTests={handleCopyTests}
        requirement={requirement}
        availableVersions={availableVersions}
        currentVersion={activeTab}
      />
    </Paper>
  );
};

export default TestManagement;
