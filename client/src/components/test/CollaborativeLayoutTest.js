import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Badge,
  Chip,
  Avatar,
  AvatarGroup,
  Button,
  TextField,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  Stack,
  IconButton,
  Tooltip,
  Card,
  CardContent,
  Grid
} from '@mui/material';
import {
  Description as DescriptionIcon,
  Chat as ChatIcon,
  Group as GroupIcon,
  Settings as SettingsIcon,
  Edit as EditIcon,
  Send as SendIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  PushPin as PinIcon,
  History as HistoryIcon,
  BugReport as TestIcon,
  LocalOffer as TagIcon
} from '@mui/icons-material';

// Mock data
const mockFeature = {
  title: "User Authentication System",
  description: "Implement a comprehensive user authentication system with multi-factor authentication, password reset functionality, and session management.",
  state: "OPEN",
  version: "v2.1",
  activeUsers: [
    { id: 1, name: "<PERSON>", avatar: "<PERSON>", color: "#1976d2", status: "editing" },
    { id: 2, name: "<PERSON>", avatar: "BS", color: "#388e3c", status: "viewing" },
    { id: 3, name: "<PERSON>", avatar: "CD", color: "#f57c00", status: "commenting" }
  ],
  comments: [
    { id: 1, user: "Alice Johnson", text: "I think we should prioritize the MFA implementation first.", time: "2 hours ago", avatar: "AJ", color: "#1976d2" },
    { id: 2, user: "Bob Smith", text: "Agreed. Should we also consider social login options?", time: "1 hour ago", avatar: "BS", color: "#388e3c" },
    { id: 3, user: "Carol Davis", text: "Yes, Google and GitHub OAuth would be valuable.", time: "30 minutes ago", avatar: "CD", color: "#f57c00" }
  ],
  team: [
    { id: 1, name: "Alice Johnson", role: "Lead Developer", tasks: 3, avatar: "AJ", color: "#1976d2" },
    { id: 2, name: "Bob Smith", role: "Backend Developer", tasks: 2, avatar: "BS", color: "#388e3c" },
    { id: 3, name: "Carol Davis", role: "Frontend Developer", tasks: 4, avatar: "CD", color: "#f57c00" },
    { id: 4, name: "David Wilson", role: "QA Engineer", tasks: 1, avatar: "DW", color: "#7b1fa2" }
  ]
};

const CollaborativeLayoutTest = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [newComment, setNewComment] = useState('');
  const [editMode, setEditMode] = useState(false);
  const [commentsVisible, setCommentsVisible] = useState(true);
  const [panelVisibility, setPanelVisibility] = useState({
    userManagement: true,
    releaseNotes: true,
    testManagement: true,
    documentHistory: true
  });

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleSendComment = () => {
    if (newComment.trim()) {
      // Add comment logic here
      setNewComment('');
    }
  };

  const togglePanelVisibility = (panel) => {
    setPanelVisibility(prev => ({
      ...prev,
      [panel]: !prev[panel]
    }));
  };

  const TabPanel = ({ children, value, index }) => (
    <div hidden={value !== index} style={{ paddingTop: '24px' }}>
      {value === index && children}
    </div>
  );

  return (
    <Box sx={{ p: 3, bgcolor: 'background.default', minHeight: '100vh' }}>
      {/* Header */}
      <Paper sx={{ p: 3, mb: 3, borderRadius: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box sx={{ flex: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
              <Typography variant="h4" component="h1">
                {mockFeature.title}
              </Typography>
              <Chip label={mockFeature.state} color="primary" size="small" />
              <Chip label={mockFeature.version} variant="outlined" size="small" />
            </Box>
            
            {/* Live Collaboration Indicators */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Active now:
              </Typography>
              <AvatarGroup max={4} sx={{ '& .MuiAvatar-root': { width: 32, height: 32, fontSize: '0.875rem' } }}>
                {mockFeature.activeUsers.map(user => (
                  <Tooltip key={user.id} title={`${user.name} (${user.status})`}>
                    <Avatar sx={{ bgcolor: user.color, width: 32, height: 32 }}>
                      {user.avatar}
                    </Avatar>
                  </Tooltip>
                ))}
              </AvatarGroup>
              <Chip 
                icon={<VisibilityIcon />} 
                label={`${mockFeature.activeUsers.length} viewing`} 
                size="small" 
                variant="outlined" 
              />
            </Box>
          </Box>
          
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              startIcon={<EditIcon />}
              onClick={() => setEditMode(!editMode)}
            >
              {editMode ? 'Save' : 'Edit'}
            </Button>
            <IconButton>
              <SettingsIcon />
            </IconButton>
          </Box>
        </Box>
      </Paper>

      {/* Always Visible Description */}
      <Paper sx={{ p: 3, mb: 3, borderRadius: 3 }}>
        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <DescriptionIcon color="primary" />
          Description
          {editMode && <Chip label="Editing" size="small" color="warning" />}
        </Typography>

        {editMode ? (
          <TextField
            fullWidth
            multiline
            rows={4}
            value={mockFeature.description}
            variant="outlined"
            sx={{ mb: 2 }}
          />
        ) : (
          <Typography variant="body1" sx={{ mb: 2, lineHeight: 1.7 }}>
            {mockFeature.description}
          </Typography>
        )}
      </Paper>

      {/* Main Content Area with Tabs and Floating Comments */}
      <Box sx={{ display: 'flex', gap: 3 }}>
        {/* Left: Tabbed Management Panels */}
        <Paper sx={{ borderRadius: 3, flex: 1 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          sx={{ borderBottom: 1, borderColor: 'divider', px: 3 }}
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab
            icon={
              <Badge badgeContent={mockFeature.team.length} color="secondary">
                <GroupIcon />
              </Badge>
            }
            label="User Management"
            iconPosition="start"
          />
          <Tab
            icon={
              <Badge badgeContent={3} color="warning">
                <TestIcon />
              </Badge>
            }
            label="Test Management"
            iconPosition="start"
          />
          <Tab
            icon={
              <Badge badgeContent={2} color="info">
                <HistoryIcon />
              </Badge>
            }
            label="Document History"
            iconPosition="start"
          />
          <Tab
            icon={
              <Badge badgeContent={5} color="success">
                <TagIcon />
              </Badge>
            }
            label="Release Tags"
            iconPosition="start"
          />
        </Tabs>

        {/* Tab 1: User Management */}
        <TabPanel value={activeTab} index={0}>
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <GroupIcon color="primary" />
              Team Members & User Management
            </Typography>

            <List>
              {mockFeature.team.map((member, index) => (
                <React.Fragment key={member.id}>
                  <ListItem>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: member.color }}>{member.avatar}</Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={member.name}
                      secondary={member.role}
                    />
                    <Stack direction="row" spacing={1}>
                      <Chip
                        label={`${member.tasks} tasks`}
                        size="small"
                        color={member.tasks > 3 ? "warning" : "default"}
                      />
                      <Button size="small" variant="outlined">Edit Roles</Button>
                    </Stack>
                  </ListItem>
                  {index < mockFeature.team.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>

            <Box sx={{ mt: 3 }}>
              <Button variant="contained" startIcon={<GroupIcon />}>
                Add Team Member
              </Button>
            </Box>
          </Box>
        </TabPanel>

        {/* Tab 2: Test Management */}
        <TabPanel value={activeTab} index={1}>
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <TestIcon color="primary" />
              Test Management
            </Typography>

            {/* Test Versions Tabs (Second Row) */}
            <Tabs value={0} sx={{ mb: 2, borderBottom: 1, borderColor: 'divider' }}>
              <Tab label="Version 2.1" />
              <Tab label="Version 2.0" />
              <Tab label="Version 1.9" />
            </Tabs>

            {/* Test Grid Placeholder */}
            <Box sx={{ border: 1, borderColor: 'divider', borderRadius: 1, p: 2, mb: 2 }}>
              <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center' }}>
                Test Grid Component Would Go Here
              </Typography>
              <Typography variant="caption" color="text.secondary" sx={{ textAlign: 'center', display: 'block' }}>
                (react-data-grid with Pass/Fail/Skip dropdowns)
              </Typography>
            </Box>

            <Stack direction="row" spacing={2}>
              <Button variant="outlined" size="small">Add Test Row</Button>
              <Button variant="outlined" size="small">Add Result Column</Button>
              <Button variant="contained" size="small">Save Tests</Button>
            </Stack>
          </Box>
        </TabPanel>

        {/* Tab 3: Document History */}
        <TabPanel value={activeTab} index={2}>
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <HistoryIcon color="primary" />
              Document History
            </Typography>

            <List>
              <ListItem>
                <ListItemText
                  primary="Software Requirements Document - v2.1"
                  secondary="Generated on Dec 15, 2024 • SRD"
                />
                <Chip label="SRD" color="primary" size="small" />
              </ListItem>
              <Divider />
              <ListItem>
                <ListItemText
                  primary="Release Notes - Sprint 23"
                  secondary="Generated on Dec 10, 2024 • Release Notes"
                />
                <Chip label="Release Notes" color="secondary" size="small" />
              </ListItem>
            </List>

            <Typography variant="caption" color="text.secondary" sx={{ mt: 2, display: 'block' }}>
              This feature has appeared in 2 documents
            </Typography>
          </Box>
        </TabPanel>

        {/* Tab 4: Release Tags */}
        <TabPanel value={activeTab} index={3}>
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <TagIcon color="primary" />
              Release Tags
            </Typography>

            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom>Current Tags:</Typography>
              <Stack direction="row" spacing={1} flexWrap="wrap" sx={{ mb: 2 }}>
                <Chip label="v2.1.0" color="primary" size="small" onDelete={() => {}} />
                <Chip label="auth-feature" color="secondary" size="small" onDelete={() => {}} />
                <Chip label="security" color="warning" size="small" onDelete={() => {}} />
                <Chip label="sprint-23" color="info" size="small" onDelete={() => {}} />
                <Chip label="high-priority" color="error" size="small" onDelete={() => {}} />
              </Stack>
            </Box>

            <Box sx={{ display: 'flex', gap: 1, mb: 3 }}>
              <TextField
                size="small"
                placeholder="Add new tag..."
                variant="outlined"
                sx={{ flexGrow: 1 }}
              />
              <Button variant="contained" size="small">Add Tag</Button>
            </Box>

            <Divider sx={{ my: 2 }} />

            <Box>
              <Typography variant="subtitle2" gutterBottom>Bulk Actions:</Typography>
              <Stack direction="row" spacing={1}>
                <Button variant="outlined" size="small">Apply to Child Requirements</Button>
                <Button variant="outlined" size="small">Export Tags</Button>
              </Stack>
            </Box>
          </Box>
        </TabPanel>
        </Paper>

        {/* Right: Floating Comments Panel */}
        {commentsVisible && (
          <Paper sx={{
            width: 400,
            borderRadius: 3,
            position: 'sticky',
            top: 24,
            height: 'fit-content',
            maxHeight: 'calc(100vh - 200px)'
          }}>
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider', display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <ChatIcon color="primary" />
                Comments
                <Badge badgeContent={mockFeature.comments.length} color="primary" />
              </Typography>
              <IconButton size="small" onClick={() => setCommentsVisible(false)}>
                <VisibilityOffIcon />
              </IconButton>
            </Box>

            {/* Comments List */}
            <List sx={{ maxHeight: 300, overflow: 'auto', p: 1 }}>
              {mockFeature.comments.map((comment, index) => (
                <React.Fragment key={comment.id}>
                  <ListItem alignItems="flex-start" sx={{ px: 1 }}>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: comment.color, width: 32, height: 32, fontSize: '0.875rem' }}>
                        {comment.avatar}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="subtitle2" sx={{ fontSize: '0.875rem' }}>{comment.user}</Typography>
                          <Typography variant="caption" color="text.secondary">{comment.time}</Typography>
                        </Box>
                      }
                      secondary={
                        <Typography variant="body2" sx={{ mt: 0.5, fontSize: '0.875rem' }}>
                          {comment.text}
                        </Typography>
                      }
                    />
                  </ListItem>
                  {index < mockFeature.comments.length - 1 && <Divider variant="inset" component="li" />}
                </React.Fragment>
              ))}
            </List>

            {/* New Comment Input */}
            <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
              <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>
                <TextField
                  fullWidth
                  multiline
                  maxRows={3}
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  placeholder="Add a comment..."
                  variant="outlined"
                  size="small"
                />
                <IconButton
                  color="primary"
                  onClick={handleSendComment}
                  disabled={!newComment.trim()}
                >
                  <SendIcon />
                </IconButton>
              </Box>
            </Box>
          </Paper>
        )}

        {/* Show Comments Button when hidden */}
        {!commentsVisible && (
          <Button
            variant="contained"
            startIcon={<ChatIcon />}
            onClick={() => setCommentsVisible(true)}
            sx={{
              position: 'fixed',
              bottom: 24,
              right: 24,
              zIndex: 1000
            }}
          >
            Comments ({mockFeature.comments.length})
          </Button>
        )}
      </Box>
    </Box>
  );
};

export default CollaborativeLayoutTest;
