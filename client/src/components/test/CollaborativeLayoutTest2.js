import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  IconButton,
  Avatar,
  AvatarGroup,
  Chip,
  Badge,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Divider,
  Stack,
  TextField,
  Tooltip,
  Card,
  CardContent,
  Grid,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Fab,
  Drawer,
  AppBar,
  Toolbar,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Edit as EditIcon,
  Settings as SettingsIcon,
  Group as GroupIcon,
  Chat as ChatIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Send as SendIcon,
  ExpandMore as ExpandMoreIcon,
  Assignment as TaskIcon,
  BugReport as TestIcon,
  History as HistoryIcon,
  LocalOffer as TagIcon,
  CheckCircle as ApproveIcon,
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  Stop as StopIcon,
  Link as LinkIcon,
  Comment as CommentIcon,
  ViewSidebar as SidebarIcon,
  Dashboard as DashboardIcon,
  Close as CloseIcon
} from '@mui/icons-material';

// Mock data
const mockFeature = {
  id: 'F-001',
  title: 'User Authentication System',
  description: `
    <h3>Overview</h3>
    <p>Implement a comprehensive user authentication system with multi-factor authentication support.</p>
    
    <h4>Key Requirements:</h4>
    <ul>
      <li>Email/password login</li>
      <li>OAuth integration (Google, GitHub)</li>
      <li>Two-factor authentication</li>
      <li>Password reset functionality</li>
    </ul>
    
    <h4>Design Mockups:</h4>
    <p><em>[Mockup images would be embedded here]</em></p>
    
    <h4>Technical Considerations:</h4>
    <p>The system should use JWT tokens for session management and integrate with our existing user management infrastructure.</p>
    
    <blockquote>
      <strong>Note:</strong> This description was updated on 2024-01-15 to include OAuth requirements. 
      <a href="#comment-5">See discussion in comments →</a>
    </blockquote>
  `,
  state: 'In Progress',
  version: 'v2.1',
  activeUsers: [
    { id: 1, name: 'Alice Johnson', avatar: 'AJ', color: '#1976d2', status: 'editing description' },
    { id: 2, name: 'Bob Smith', avatar: 'BS', color: '#388e3c', status: 'reviewing tests' },
    { id: 3, name: 'Carol Davis', avatar: 'CD', color: '#f57c00', status: 'viewing' }
  ],
  team: [
    { id: 1, name: 'Alice Johnson', role: 'Lead Developer', avatar: 'AJ', color: '#1976d2', tasks: 3, canApprove: true },
    { id: 2, name: 'Bob Smith', role: 'QA Engineer', avatar: 'BS', color: '#388e3c', tasks: 2, canApprove: false },
    { id: 3, name: 'Carol Davis', role: 'Product Manager', avatar: 'CD', color: '#f57c00', tasks: 1, canApprove: true }
  ],
  comments: [
    { id: 1, author: 'Alice Johnson', avatar: 'AJ', color: '#1976d2', time: '2 hours ago', text: 'I think we should prioritize the OAuth integration first since it affects the overall architecture.', linkedToDescription: false },
    { id: 2, author: 'Bob Smith', avatar: 'BS', color: '#388e3c', time: '1 hour ago', text: 'Agreed. I\'ve started writing test cases for the OAuth flow. Should we support SAML as well?', linkedToDescription: false },
    { id: 3, author: 'Carol Davis', avatar: 'CD', color: '#f57c00', time: '45 minutes ago', text: 'Let\'s stick with OAuth for now. We can add SAML in a future iteration if customers request it.', linkedToDescription: false },
    { id: 4, author: 'Alice Johnson', avatar: 'AJ', color: '#1976d2', time: '30 minutes ago', text: 'Perfect. I\'ll update the technical requirements accordingly.', linkedToDescription: false },
    { id: 5, author: 'Carol Davis', avatar: 'CD', color: '#f57c00', time: '15 minutes ago', text: 'I\'ve added OAuth requirements to the description. This affects our timeline - we might need an extra sprint.', linkedToDescription: true }
  ],
  tasks: [
    { id: 1, title: 'Set up OAuth providers', assignee: 'Alice Johnson', status: 'active', priority: 'high' },
    { id: 2, title: 'Design login UI mockups', assignee: 'Carol Davis', status: 'completed', priority: 'medium' },
    { id: 3, title: 'Write authentication tests', assignee: 'Bob Smith', status: 'active', priority: 'high' }
  ],
  testResults: { pass: 12, fail: 3, skip: 2 },
  releaseTags: ['v2.1-auth', 'security-update'],
  documentHistory: 3
};

const CollaborativeLayoutTest2 = () => {
  const [layoutMode, setLayoutMode] = useState('split'); // 'split', 'sidebar', 'compact'
  const [activeTab, setActiveTab] = useState(0);
  const [newComment, setNewComment] = useState('');
  const [editMode, setEditMode] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [compactPanelsExpanded, setCompactPanelsExpanded] = useState({
    tasks: false,
    tests: false,
    docs: false,
    tags: false
  });

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleSendComment = () => {
    if (newComment.trim()) {
      // Add comment logic here
      setNewComment('');
    }
  };

  const toggleCompactPanel = (panel) => {
    setCompactPanelsExpanded(prev => ({
      ...prev,
      [panel]: !prev[panel]
    }));
  };

  const LayoutModeSelector = () => (
    <Paper sx={{ p: 2, mb: 3, borderRadius: 3 }}>
      <Typography variant="h6" gutterBottom>Layout Test Mode</Typography>
      <Stack direction="row" spacing={2}>
        <Button 
          variant={layoutMode === 'split' ? 'contained' : 'outlined'}
          onClick={() => setLayoutMode('split')}
        >
          Split View
        </Button>
        <Button 
          variant={layoutMode === 'sidebar' ? 'contained' : 'outlined'}
          onClick={() => setLayoutMode('sidebar')}
        >
          Sidebar
        </Button>
        <Button 
          variant={layoutMode === 'compact' ? 'contained' : 'outlined'}
          onClick={() => setLayoutMode('compact')}
        >
          Compact Cards
        </Button>
      </Stack>
    </Paper>
  );

  const HeaderSection = () => (
    <Paper sx={{ p: 3, mb: 3, borderRadius: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
        <Box sx={{ flex: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            <Typography variant="h4" component="h1">
              {mockFeature.title}
            </Typography>
            <Chip label={mockFeature.state} color="primary" size="small" />
            <Chip label={mockFeature.version} variant="outlined" size="small" />
          </Box>
          
          {/* Live Collaboration Indicators */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            <Typography variant="body2" color="text.secondary">
              Active now:
            </Typography>
            <AvatarGroup max={4} sx={{ '& .MuiAvatar-root': { width: 32, height: 32, fontSize: '0.875rem' } }}>
              {mockFeature.activeUsers.map(user => (
                <Tooltip key={user.id} title={`${user.name} (${user.status})`}>
                  <Avatar sx={{ bgcolor: user.color, width: 32, height: 32 }}>
                    {user.avatar}
                  </Avatar>
                </Tooltip>
              ))}
            </AvatarGroup>
            <Chip 
              icon={<VisibilityIcon />} 
              label={`${mockFeature.activeUsers.length} viewing`} 
              size="small" 
              variant="outlined" 
            />
          </Box>
        </Box>
        
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<EditIcon />}
            onClick={() => setEditMode(!editMode)}
          >
            {editMode ? 'Save' : 'Edit'}
          </Button>
          <IconButton>
            <SettingsIcon />
          </IconButton>
        </Box>
      </Box>
    </Paper>
  );

  return (
    <Box sx={{ p: 3, bgcolor: 'background.default', minHeight: '100vh' }}>
      <LayoutModeSelector />
      <HeaderSection />
      
      {/* Render different layouts based on mode */}
      {layoutMode === 'split' && <SplitViewLayout />}
      {layoutMode === 'sidebar' && <SidebarLayout />}
      {layoutMode === 'compact' && <CompactCardsLayout />}
    </Box>
  );

  // Split View Layout - Description left, Comments right, management below
  function SplitViewLayout() {
    return (
      <>
        {/* Description and Comments Side by Side */}
        <Box sx={{ display: 'flex', gap: 3, mb: 3 }}>
          {/* Left: Description */}
          <Paper sx={{ flex: 2, p: 3, borderRadius: 3 }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <EditIcon color="primary" />
              Description
              {editMode && <Chip label="Editing" color="warning" size="small" />}
            </Typography>
            <Box 
              sx={{ 
                minHeight: 400,
                border: editMode ? '2px dashed #1976d2' : 'none',
                borderRadius: 1,
                p: editMode ? 2 : 0
              }}
              dangerouslySetInnerHTML={{ __html: mockFeature.description }}
            />
          </Paper>

          {/* Right: Comments */}
          <Paper sx={{ flex: 1, borderRadius: 3, height: 'fit-content', position: 'sticky', top: 24 }}>
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
              <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <ChatIcon color="primary" />
                Comments
                <Badge badgeContent={mockFeature.comments.length} color="primary" />
              </Typography>
            </Box>
            
            <Box sx={{ maxHeight: 400, overflow: 'auto', p: 2 }}>
              {mockFeature.comments.map((comment) => (
                <Box key={comment.id} sx={{ mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                    <Avatar sx={{ bgcolor: comment.color, width: 32, height: 32, fontSize: '0.75rem' }}>
                      {comment.avatar}
                    </Avatar>
                    <Box sx={{ flex: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                        <Typography variant="subtitle2">{comment.author}</Typography>
                        <Typography variant="caption" color="text.secondary">{comment.time}</Typography>
                        {comment.linkedToDescription && (
                          <Tooltip title="Linked to description change">
                            <LinkIcon sx={{ fontSize: 16, color: 'primary.main' }} />
                          </Tooltip>
                        )}
                      </Box>
                      <Typography variant="body2">{comment.text}</Typography>
                    </Box>
                  </Box>
                </Box>
              ))}
            </Box>
            
            <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
              <TextField
                fullWidth
                multiline
                rows={2}
                placeholder="Add a comment..."
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                sx={{ mb: 1 }}
              />
              <Button
                variant="contained"
                endIcon={<SendIcon />}
                onClick={handleSendComment}
                disabled={!newComment.trim()}
                size="small"
              >
                Send
              </Button>
            </Box>
          </Paper>
        </Box>

        {/* Management Panels Below */}
        <CompactManagementPanels />
      </>
    );
  }

  // Sidebar Layout - Description main, sidebar with comments and management
  function SidebarLayout() {
    return (
      <Box sx={{ display: 'flex', gap: 3 }}>
        {/* Main Content */}
        <Box sx={{ flex: 1 }}>
          <Paper sx={{ p: 3, borderRadius: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <EditIcon color="primary" />
              Description
              {editMode && <Chip label="Editing" color="warning" size="small" />}
            </Typography>
            <Box
              sx={{
                minHeight: 600,
                border: editMode ? '2px dashed #1976d2' : 'none',
                borderRadius: 1,
                p: editMode ? 2 : 0
              }}
              dangerouslySetInnerHTML={{ __html: mockFeature.description }}
            />
          </Paper>
        </Box>

        {/* Sidebar */}
        <Box sx={{ width: 400 }}>
          {/* Comments */}
          <Paper sx={{ borderRadius: 3, mb: 3 }}>
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
              <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <ChatIcon color="primary" />
                Comments
                <Badge badgeContent={mockFeature.comments.length} color="primary" />
              </Typography>
            </Box>

            <Box sx={{ maxHeight: 300, overflow: 'auto', p: 2 }}>
              {mockFeature.comments.map((comment) => (
                <Box key={comment.id} sx={{ mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                    <Avatar sx={{ bgcolor: comment.color, width: 28, height: 28, fontSize: '0.7rem' }}>
                      {comment.avatar}
                    </Avatar>
                    <Box sx={{ flex: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                        <Typography variant="caption" fontWeight="bold">{comment.author}</Typography>
                        <Typography variant="caption" color="text.secondary">{comment.time}</Typography>
                        {comment.linkedToDescription && (
                          <Tooltip title="Linked to description change">
                            <LinkIcon sx={{ fontSize: 12, color: 'primary.main' }} />
                          </Tooltip>
                        )}
                      </Box>
                      <Typography variant="body2" fontSize="0.8rem">{comment.text}</Typography>
                    </Box>
                  </Box>
                </Box>
              ))}
            </Box>

            <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
              <TextField
                fullWidth
                multiline
                rows={2}
                placeholder="Add a comment..."
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                sx={{ mb: 1 }}
                size="small"
              />
              <Button
                variant="contained"
                endIcon={<SendIcon />}
                onClick={handleSendComment}
                disabled={!newComment.trim()}
                size="small"
                fullWidth
              >
                Send
              </Button>
            </Box>
          </Paper>

          {/* Compact Management in Sidebar */}
          <SidebarManagementPanels />
        </Box>
      </Box>
    );
  }

  // Compact Cards Layout - Spotify-style grouped cards
  function CompactCardsLayout() {
    return (
      <>
        {/* Description takes full width */}
        <Paper sx={{ p: 3, borderRadius: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <EditIcon color="primary" />
            Description
            {editMode && <Chip label="Editing" color="warning" size="small" />}
          </Typography>
          <Box
            sx={{
              minHeight: 300,
              border: editMode ? '2px dashed #1976d2' : 'none',
              borderRadius: 1,
              p: editMode ? 2 : 0
            }}
            dangerouslySetInnerHTML={{ __html: mockFeature.description }}
          />
        </Paper>

        {/* Spotify-style compact cards */}
        <Grid container spacing={2}>
          {/* Comments Card */}
          <Grid item xs={12} md={6}>
            <Card sx={{ height: 400, borderRadius: 3 }}>
              <CardContent sx={{ p: 2, height: '100%', display: 'flex', flexDirection: 'column' }}>
                <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                  <ChatIcon color="primary" />
                  Comments
                  <Badge badgeContent={mockFeature.comments.length} color="primary" />
                </Typography>

                <Box sx={{ flex: 1, overflow: 'auto', mb: 2 }}>
                  {mockFeature.comments.slice(0, 3).map((comment) => (
                    <Box key={comment.id} sx={{ mb: 1.5 }}>
                      <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                        <Avatar sx={{ bgcolor: comment.color, width: 24, height: 24, fontSize: '0.6rem' }}>
                          {comment.avatar}
                        </Avatar>
                        <Box sx={{ flex: 1 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                            <Typography variant="caption" fontWeight="bold">{comment.author}</Typography>
                            <Typography variant="caption" color="text.secondary">{comment.time}</Typography>
                          </Box>
                          <Typography variant="body2" fontSize="0.75rem" sx={{
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical'
                          }}>
                            {comment.text}
                          </Typography>
                        </Box>
                      </Box>
                    </Box>
                  ))}
                </Box>

                <TextField
                  fullWidth
                  placeholder="Add comment..."
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  size="small"
                  InputProps={{
                    endAdornment: (
                      <IconButton size="small" onClick={handleSendComment} disabled={!newComment.trim()}>
                        <SendIcon fontSize="small" />
                      </IconButton>
                    )
                  }}
                />
              </CardContent>
            </Card>
          </Grid>

          {/* Team & Tasks Card */}
          <Grid item xs={12} md={6}>
            <SpotifyStyleManagementCard />
          </Grid>

          {/* Additional compact cards for other features */}
          <Grid item xs={12} md={3}>
            <CompactFeatureCard
              title="Tests"
              icon={<TestIcon />}
              data={mockFeature.testResults}
              color="warning"
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <CompactFeatureCard
              title="Tags"
              icon={<TagIcon />}
              data={mockFeature.releaseTags}
              color="info"
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <CompactFeatureCard
              title="Documents"
              icon={<HistoryIcon />}
              data={mockFeature.documentHistory}
              color="success"
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <CompactFeatureCard
              title="Approvals"
              icon={<ApproveIcon />}
              data={{ pending: 2, approved: 1 }}
              color="primary"
            />
          </Grid>
        </Grid>
      </>
    );
  }

  // Helper Components
  function CompactManagementPanels() {
    return (
      <Grid container spacing={2}>
        {/* Team & Tasks */}
        <Grid item xs={12} md={6}>
          <Accordion expanded={compactPanelsExpanded.tasks} onChange={() => toggleCompactPanel('tasks')}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                <GroupIcon color="primary" />
                <Typography variant="h6">Team & Tasks</Typography>
                <Box sx={{ ml: 'auto', display: 'flex', gap: 1 }}>
                  <Chip label={`${mockFeature.team.length} members`} size="small" />
                  <Chip label={`${mockFeature.tasks.length} tasks`} size="small" color="warning" />
                </Box>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <TeamTasksPanel />
            </AccordionDetails>
          </Accordion>
        </Grid>

        {/* Tests */}
        <Grid item xs={12} md={6}>
          <Accordion expanded={compactPanelsExpanded.tests} onChange={() => toggleCompactPanel('tests')}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                <TestIcon color="warning" />
                <Typography variant="h6">Tests</Typography>
                <Box sx={{ ml: 'auto', display: 'flex', gap: 1 }}>
                  <Chip label={`${mockFeature.testResults.pass} pass`} size="small" color="success" />
                  <Chip label={`${mockFeature.testResults.fail} fail`} size="small" color="error" />
                </Box>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2">Test grid would go here...</Typography>
            </AccordionDetails>
          </Accordion>
        </Grid>

        {/* Release Tags */}
        <Grid item xs={12} md={6}>
          <Accordion expanded={compactPanelsExpanded.tags} onChange={() => toggleCompactPanel('tags')}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                <TagIcon color="info" />
                <Typography variant="h6">Release Tags</Typography>
                <Box sx={{ ml: 'auto', display: 'flex', gap: 1 }}>
                  {mockFeature.releaseTags.map(tag => (
                    <Chip key={tag} label={tag} size="small" variant="outlined" />
                  ))}
                </Box>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2">Tag management would go here...</Typography>
            </AccordionDetails>
          </Accordion>
        </Grid>

        {/* Document History */}
        <Grid item xs={12} md={6}>
          <Accordion expanded={compactPanelsExpanded.docs} onChange={() => toggleCompactPanel('docs')}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                <HistoryIcon color="success" />
                <Typography variant="h6">Documents</Typography>
                <Box sx={{ ml: 'auto' }}>
                  <Chip label={`${mockFeature.documentHistory} docs`} size="small" color="success" />
                </Box>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2">Document history would go here...</Typography>
            </AccordionDetails>
          </Accordion>
        </Grid>
      </Grid>
    );
  }

  function SidebarManagementPanels() {
    return (
      <Stack spacing={2}>
        {/* Quick Team Overview */}
        <Card sx={{ borderRadius: 3 }}>
          <CardContent sx={{ p: 2 }}>
            <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
              <GroupIcon color="primary" />
              Team
              <Badge badgeContent={mockFeature.team.length} color="secondary" />
            </Typography>
            <AvatarGroup max={3} sx={{ justifyContent: 'flex-start', '& .MuiAvatar-root': { width: 28, height: 28, fontSize: '0.7rem' } }}>
              {mockFeature.team.map(member => (
                <Tooltip key={member.id} title={member.name}>
                  <Avatar sx={{ bgcolor: member.color }}>
                    {member.avatar}
                  </Avatar>
                </Tooltip>
              ))}
            </AvatarGroup>
          </CardContent>
        </Card>

        {/* Quick Stats */}
        <Card sx={{ borderRadius: 3 }}>
          <CardContent sx={{ p: 2 }}>
            <Typography variant="subtitle1" gutterBottom>Quick Stats</Typography>
            <Stack spacing={1}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="body2">Tests</Typography>
                <Box sx={{ display: 'flex', gap: 0.5 }}>
                  <Chip label={mockFeature.testResults.pass} size="small" color="success" />
                  <Chip label={mockFeature.testResults.fail} size="small" color="error" />
                </Box>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="body2">Tags</Typography>
                <Chip label={mockFeature.releaseTags.length} size="small" color="info" />
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="body2">Documents</Typography>
                <Chip label={mockFeature.documentHistory} size="small" color="success" />
              </Box>
            </Stack>
          </CardContent>
        </Card>
      </Stack>
    );
  }

  function SpotifyStyleManagementCard() {
    return (
      <Card sx={{ height: 400, borderRadius: 3 }}>
        <CardContent sx={{ p: 2, height: '100%', display: 'flex', flexDirection: 'column' }}>
          <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
            <GroupIcon color="primary" />
            Team & Tasks
            <Badge badgeContent={mockFeature.team.length} color="secondary" />
          </Typography>

          <Box sx={{ flex: 1, overflow: 'auto' }}>
            {mockFeature.team.map((member) => (
              <Box key={member.id} sx={{ mb: 2, p: 1, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <Avatar sx={{ bgcolor: member.color, width: 28, height: 28, fontSize: '0.7rem' }}>
                    {member.avatar}
                  </Avatar>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="subtitle2">{member.name}</Typography>
                    <Typography variant="caption" color="text.secondary">{member.role}</Typography>
                  </Box>
                  <Stack direction="row" spacing={0.5}>
                    <Chip label={`${member.tasks} tasks`} size="small" color={member.tasks > 2 ? "warning" : "default"} />
                    {member.canApprove && <Chip label="Approver" size="small" color="success" />}
                  </Stack>
                </Box>

                {/* Quick task actions */}
                <Stack direction="row" spacing={1}>
                  <Button size="small" variant="outlined" startIcon={<PlayIcon />}>
                    Start Task
                  </Button>
                  <Button size="small" variant="outlined" startIcon={<ApproveIcon />} disabled={!member.canApprove}>
                    Approve
                  </Button>
                </Stack>
              </Box>
            ))}
          </Box>

          <Button variant="contained" startIcon={<GroupIcon />} size="small" fullWidth>
            Add Team Member
          </Button>
        </CardContent>
      </Card>
    );
  }

  function CompactFeatureCard({ title, icon, data, color }) {
    return (
      <Card sx={{ height: 150, borderRadius: 3, cursor: 'pointer', '&:hover': { boxShadow: 4 } }}>
        <CardContent sx={{ p: 2, height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' }}>
          <Box sx={{ color: `${color}.main`, mb: 1 }}>
            {icon}
          </Box>
          <Typography variant="h6" gutterBottom textAlign="center">
            {title}
          </Typography>

          {/* Render different data types */}
          {typeof data === 'object' && data.pass !== undefined ? (
            <Stack direction="row" spacing={1}>
              <Chip label={`${data.pass} pass`} size="small" color="success" />
              <Chip label={`${data.fail} fail`} size="small" color="error" />
            </Stack>
          ) : Array.isArray(data) ? (
            <Stack direction="row" spacing={0.5} flexWrap="wrap" justifyContent="center">
              {data.slice(0, 2).map((item, index) => (
                <Chip key={index} label={item} size="small" variant="outlined" />
              ))}
              {data.length > 2 && <Chip label={`+${data.length - 2}`} size="small" />}
            </Stack>
          ) : typeof data === 'object' ? (
            <Stack direction="row" spacing={1}>
              {Object.entries(data).map(([key, value]) => (
                <Chip key={key} label={`${value} ${key}`} size="small" color={key === 'pending' ? 'warning' : 'success'} />
              ))}
            </Stack>
          ) : (
            <Chip label={data} size="small" color={color} />
          )}
        </CardContent>
      </Card>
    );
  }

  function TeamTasksPanel() {
    return (
      <Box>
        {mockFeature.team.map((member) => (
          <Box key={member.id} sx={{ mb: 2, p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
              <Avatar sx={{ bgcolor: member.color }}>
                {member.avatar}
              </Avatar>
              <Box sx={{ flex: 1 }}>
                <Typography variant="subtitle1">{member.name}</Typography>
                <Typography variant="body2" color="text.secondary">{member.role}</Typography>
              </Box>
              <Stack direction="row" spacing={1}>
                <Chip label={`${member.tasks} tasks`} size="small" color={member.tasks > 2 ? "warning" : "default"} />
                {member.canApprove && <Chip label="Can Approve" size="small" color="success" />}
              </Stack>
            </Box>

            {/* Task management actions */}
            <Stack direction="row" spacing={1} flexWrap="wrap">
              <Button size="small" variant="outlined" startIcon={<TaskIcon />}>
                Assign Task
              </Button>
              <Button size="small" variant="outlined" startIcon={<PlayIcon />}>
                Start Task
              </Button>
              <Button size="small" variant="outlined" startIcon={<StopIcon />}>
                Complete Task
              </Button>
              {member.canApprove && (
                <Button size="small" variant="contained" startIcon={<ApproveIcon />} color="success">
                  Approve Version
                </Button>
              )}
            </Stack>
          </Box>
        ))}

        <Button variant="contained" startIcon={<GroupIcon />} fullWidth>
          Add Team Member
        </Button>
      </Box>
    );
  }
};

export default CollaborativeLayoutTest2;
